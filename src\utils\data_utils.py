"""
数据处理工具函数
提供数据加载、解析、格式化等功能
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Optional, Any
from pathlib import Path
import logging

from config.settings import get_settings
from config.logging_config import get_logger

logger = get_logger('data_utils')


def load_data(file_path: Optional[str] = None) -> pd.DataFrame:
    """
    加载大乐透历史数据
    
    Args:
        file_path: CSV文件路径，如果为None则使用配置中的路径
        
    Returns:
        DataFrame: 包含历史开奖数据的DataFrame
        
    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 数据格式错误
    """
    settings = get_settings()
    
    if file_path is None:
        file_path = settings.data.data_file
    
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"数据文件不存在: {file_path}")
    
    logger.info(f"加载数据文件: {file_path}")
    
    try:
        df = pd.read_csv(file_path)
        
        # 验证必要的列
        required_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"数据文件缺少必要列: {missing_columns}")
        
        # 确保数据按期号从旧到新排序
        df = df.sort_values('期号', ascending=True).reset_index(drop=True)
        
        logger.info(f"成功加载数据，共 {len(df)} 期")
        return df
        
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        raise


def parse_numbers(data) -> Tuple[List[int], List[int]]:
    """
    解析数据中的红球和蓝球号码

    Args:
        data: DataFrame的一行数据(pd.Series)或字符串格式的号码

    Returns:
        Tuple[List[int], List[int]]: (红球号码列表, 蓝球号码列表)

    Raises:
        ValueError: 号码格式错误
    """
    try:
        # 如果是字符串格式，解析为号码列表
        if isinstance(data, str):
            numbers = [int(x.strip()) for x in data.split()]
            if len(numbers) >= 7:  # 需要至少7个数字（5红球+2蓝球）
                red_balls = numbers[:5]
                blue_balls = numbers[5:7]  # 取两个蓝球
            elif len(numbers) >= 6:  # 如果只有6个数字，补充一个蓝球
                red_balls = numbers[:5]
                blue_balls = [numbers[5], 1]  # 第二个蓝球默认为1
            elif len(numbers) >= 5:  # 如果只有5个数字，补充两个蓝球
                red_balls = numbers[:5]
                blue_balls = [1, 2]  # 默认蓝球
            else:
                raise ValueError(f"字符串格式号码数量不足: {data}")

        # 如果是pandas Series，按原来的方式解析
        elif hasattr(data, '__getitem__') and hasattr(data, 'get'):
            red_balls = [int(data[f'红球{i}']) for i in range(1, 6)]
            blue_balls = [int(data[f'蓝球{i}']) for i in range(1, 3)]  # 取两个蓝球

        else:
            raise ValueError(f"不支持的数据类型: {type(data)}")

        # 验证号码范围
        if not all(1 <= num <= 35 for num in red_balls):
            raise ValueError(f"红球号码超出范围: {red_balls}")

        if not all(1 <= num <= 12 for num in blue_balls):
            raise ValueError(f"蓝球号码超出范围: {blue_balls}")

        return red_balls, blue_balls

    except (KeyError, ValueError) as e:
        logger.error(f"解析号码失败: {e}")
        raise ValueError(f"号码格式错误: {e}")


def format_numbers(numbers: List[int]) -> str:
    """
    格式化号码为两位数字符串
    
    Args:
        numbers: 号码列表
        
    Returns:
        str: 格式化后的号码字符串，如"01,03,07"
    """
    return ','.join([f"{num:02d}" for num in sorted(numbers)])


def validate_data(df: pd.DataFrame) -> Tuple[bool, List[str]]:
    """
    验证数据的完整性和正确性
    
    Args:
        df: 要验证的DataFrame
        
    Returns:
        Tuple[bool, List[str]]: (是否有效, 错误信息列表)
    """
    errors = []
    
    try:
        # 检查必要列
        required_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            errors.append(f"缺少必要列: {missing_columns}")
        
        # 检查数据类型
        for i in range(1, 6):
            col = f'红球{i}'
            if col in df.columns and not pd.api.types.is_numeric_dtype(df[col]):
                errors.append(f"列 {col} 不是数值类型")
        
        for i in range(1, 3):
            col = f'蓝球{i}'
            if col in df.columns and not pd.api.types.is_numeric_dtype(df[col]):
                errors.append(f"列 {col} 不是数值类型")
        
        # 检查号码范围
        for _, row in df.iterrows():
            try:
                red_balls, blue_balls = parse_numbers(row)
                
                # 检查红球重复
                if len(set(red_balls)) != 5:
                    errors.append(f"期号 {row['期号']}: 红球有重复")
                
                # 检查蓝球重复
                if len(set(blue_balls)) != 2:
                    errors.append(f"期号 {row['期号']}: 蓝球有重复")
                    
            except ValueError as e:
                errors.append(f"期号 {row['期号']}: {e}")
        
        # 检查期号连续性
        periods = sorted(df['期号'].tolist())
        for i in range(1, len(periods)):
            if periods[i] - periods[i-1] != 1:
                logger.warning(f"期号不连续: {periods[i-1]} -> {periods[i]}")
        
        is_valid = len(errors) == 0
        
        if is_valid:
            logger.info("数据验证通过")
        else:
            logger.warning(f"数据验证失败，发现 {len(errors)} 个错误")
        
        return is_valid, errors
        
    except Exception as e:
        logger.error(f"数据验证过程出错: {e}")
        return False, [f"验证过程出错: {e}"]


def clean_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    清理数据，移除无效记录
    
    Args:
        df: 原始DataFrame
        
    Returns:
        DataFrame: 清理后的DataFrame
    """
    logger.info("开始清理数据")
    
    original_count = len(df)
    cleaned_df = df.copy()
    
    # 移除空值行
    cleaned_df = cleaned_df.dropna()
    
    # 移除重复期号
    cleaned_df = cleaned_df.drop_duplicates(subset=['期号'])
    
    # 验证每行数据
    valid_rows = []
    for _, row in cleaned_df.iterrows():
        try:
            parse_numbers(row)
            valid_rows.append(row)
        except ValueError:
            logger.warning(f"移除无效数据行: 期号 {row['期号']}")
    
    if valid_rows:
        cleaned_df = pd.DataFrame(valid_rows).reset_index(drop=True)
    else:
        cleaned_df = pd.DataFrame()
    
    removed_count = original_count - len(cleaned_df)
    
    if removed_count > 0:
        logger.info(f"数据清理完成，移除 {removed_count} 行无效数据")
    else:
        logger.info("数据清理完成，无需移除数据")
    
    return cleaned_df


def save_processed_data(df: pd.DataFrame, filename: str) -> None:
    """
    保存处理后的数据
    
    Args:
        df: 要保存的DataFrame
        filename: 文件名
    """
    settings = get_settings()
    output_dir = Path(settings.data.processed_data_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    output_path = output_dir / filename
    df.to_csv(output_path, index=False, encoding='utf-8')
    
    logger.info(f"保存处理后的数据到: {output_path}")


def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """
    获取数据摘要信息
    
    Args:
        df: DataFrame
        
    Returns:
        Dict[str, Any]: 数据摘要
    """
    if df.empty:
        return {"error": "数据为空"}
    
    summary = {
        "total_periods": len(df),
        "period_range": {
            "start": df['期号'].min(),
            "end": df['期号'].max()
        },
        "data_quality": {
            "missing_values": df.isnull().sum().sum(),
            "duplicate_periods": df['期号'].duplicated().sum()
        }
    }
    
    # 添加日期信息（如果存在）
    if '日期' in df.columns:
        summary["date_range"] = {
            "start": df['日期'].min(),
            "end": df['日期'].max()
        }
    
    return summary
