#!/usr/bin/env python3
"""
重新训练所有系统以适应新的数据排序
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def retrain_bayes_parameters():
    """重新训练贝叶斯参数"""
    print("🧠 重新训练贝叶斯参数...")
    
    try:
        from src.tools.optimize_bayes_parameters import main as optimize_bayes
        print("  📊 开始贝叶斯参数优化...")
        optimize_bayes()
        print("  ✅ 贝叶斯参数优化完成")
        return True
    except Exception as e:
        print(f"  ❌ 贝叶斯参数优化失败: {e}")
        return False

def retrain_predictor_parameters():
    """重新训练预测器参数"""
    print("🎯 重新训练预测器参数...")
    
    try:
        from src.tools.optimize_predictor_parameters_unified import main as optimize_predictor
        print("  📊 开始预测器参数优化...")
        optimize_predictor()
        print("  ✅ 预测器参数优化完成")
        return True
    except Exception as e:
        print(f"  ❌ 预测器参数优化失败: {e}")
        return False

def test_system_after_retrain():
    """测试重新训练后的系统"""
    print("🧪 测试重新训练后的系统...")
    
    try:
        from src.apps.main import main as test_main
        print("  🚀 运行主系统测试...")
        test_main()
        print("  ✅ 主系统测试完成")
        return True
    except Exception as e:
        print(f"  ❌ 主系统测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 开始重新训练所有系统")
    print("=" * 60)
    
    # 步骤1：重新训练贝叶斯参数
    print("\n📈 步骤1：重新训练贝叶斯参数")
    bayes_success = retrain_bayes_parameters()
    
    # 步骤2：重新训练预测器参数
    print("\n📈 步骤2：重新训练预测器参数")
    predictor_success = retrain_predictor_parameters()
    
    # 步骤3：测试系统
    print("\n📈 步骤3：测试重新训练后的系统")
    test_success = test_system_after_retrain()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 重新训练总结：")
    print(f"  贝叶斯参数：{'✅ 成功' if bayes_success else '❌ 失败'}")
    print(f"  预测器参数：{'✅ 成功' if predictor_success else '❌ 失败'}")
    print(f"  系统测试：{'✅ 成功' if test_success else '❌ 失败'}")
    
    if bayes_success and predictor_success:
        print("\n🎉 所有系统重新训练完成！")
        print("💡 建议：运行主程序验证预测效果")
    else:
        print("\n⚠️  部分系统重新训练失败，请检查错误信息")
    
    return bayes_success and predictor_success

if __name__ == "__main__":
    main()
