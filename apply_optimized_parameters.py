#!/usr/bin/env python3
"""
应用优化参数脚本
将优化后的参数应用到预测器中
"""

import sys
import os
import json
from typing import Dict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def update_improved_predictor_parameters(params: Dict):
    """更新 ImprovedPredictor 的参数"""
    
    predictor_file = "src/models/improved_predictor.py"
    
    # 读取原文件
    with open(predictor_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 构建新的参数字符串
    strategy_weights_str = f"""        self.strategy_weights = {{
            'trend_following': {params['strategy_weights']['trend_following']:.3f},    # 趋势跟随
            'mean_reversion': {params['strategy_weights']['mean_reversion']:.3f},     # 均值回归
            'persistence': {params['strategy_weights']['persistence']:.3f},        # 状态持续
            'frequency_based': {params['strategy_weights']['frequency_based']:.3f}     # 基于号码频率
        }}"""
    
    feature_predictability_str = f"""        self.feature_predictability = {{
            'blue_size': {params['feature_predictability']['blue_size']:.3f},
            'red_odd_even': {params['feature_predictability']['red_odd_even']:.3f},   # 优化后的置信度
            'red_size': {params['feature_predictability']['red_size']:.3f}        # 优化后的置信度
        }}"""
    
    bayes_weights_str = f"""                weights = {params['bayes_weights']}  # 优化后的权重"""
    bayes_confidences_str = f"""                confidences = {params['bayes_confidences']}  # 优化后的置信度"""
    
    # 替换参数
    import re
    
    # 替换策略权重
    strategy_pattern = r'self\.strategy_weights = \{[^}]+\}'
    content = re.sub(strategy_pattern, strategy_weights_str.strip(), content, flags=re.DOTALL)
    
    # 替换特征可预测性
    feature_pattern = r'self\.feature_predictability = \{[^}]+\}'
    content = re.sub(feature_pattern, feature_predictability_str.strip(), content, flags=re.DOTALL)
    
    # 替换贝叶斯权重
    bayes_weights_pattern = r'weights = \[0\.\d+, 0\.\d+, 0\.\d+, 0\.\d+\]  # 马尔科夫、频率、趋势、模式[^#]*'
    content = re.sub(bayes_weights_pattern, bayes_weights_str.strip(), content)
    
    # 替换贝叶斯置信度
    bayes_conf_pattern = r'confidences = \[0\.\d+, 0\.\d+, 0\.\d+, 0\.\d+\]  # 对应的置信度[^#]*'
    content = re.sub(bayes_conf_pattern, bayes_confidences_str.strip(), content)
    
    # 备份原文件
    backup_file = f"{predictor_file}.backup"
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(open(predictor_file, 'r', encoding='utf-8').read())
    
    # 写入新文件
    with open(predictor_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 已更新 {predictor_file}")
    print(f"📁 原文件备份为 {backup_file}")


def load_optimization_result(filename: str) -> Dict:
    """加载优化结果"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)


def display_parameter_comparison(old_params: Dict, new_params: Dict):
    """显示参数对比"""
    print("\n📊 参数对比:")
    print("=" * 60)
    
    print("\n🔧 策略权重:")
    for key in old_params['strategy_weights']:
        old_val = old_params['strategy_weights'][key]
        new_val = new_params['strategy_weights'][key]
        change = new_val - old_val
        print(f"  {key:15}: {old_val:.3f} -> {new_val:.3f} ({change:+.3f})")
    
    print("\n🎯 特征可预测性:")
    for key in old_params['feature_predictability']:
        old_val = old_params['feature_predictability'][key]
        new_val = new_params['feature_predictability'][key]
        change = new_val - old_val
        print(f"  {key:15}: {old_val:.3f} -> {new_val:.3f} ({change:+.3f})")


def main():
    """主函数"""
    print("🔧 应用优化参数工具")
    print("=" * 60)
    
    # 当前参数（基线）
    current_params = {
        'strategy_weights': {
            'trend_following': 0.40,
            'mean_reversion': 0.30,
            'persistence': 0.20,
            'frequency_based': 0.10
        },
        'feature_predictability': {
            'red_odd_even': 0.60,
            'red_size': 0.55,
            'blue_size': 0.473
        },
        'bayes_weights': [0.40, 0.30, 0.20, 0.10],
        'bayes_confidences': [0.9, 0.8, 0.7, 0.6]
    }
    
    # 选择优化结果文件
    print("\n1. 选择优化结果文件:")
    
    # 查找优化结果文件
    import glob
    result_files = glob.glob("optimized_parameters_*.json")
    
    if not result_files:
        print("❌ 未找到优化结果文件")
        print("请先运行 optimize_predictor_parameters.py 进行参数优化")
        return
    
    print("找到以下优化结果文件:")
    for i, filename in enumerate(result_files, 1):
        print(f"  {i}) {filename}")
    
    choice = input(f"请选择文件 (1-{len(result_files)}): ").strip()
    
    try:
        file_index = int(choice) - 1
        selected_file = result_files[file_index]
    except (ValueError, IndexError):
        print("❌ 无效选择")
        return
    
    # 加载优化结果
    print(f"\n📂 加载优化结果: {selected_file}")
    optimization_result = load_optimization_result(selected_file)
    
    best_params = optimization_result['best_params']
    
    # 显示优化信息
    print(f"\n📈 优化信息:")
    print(f"  优化方法: {optimization_result['method']}")
    print(f"  最佳得分: {optimization_result['best_score']:.4f}")
    print(f"  优化时间: {optimization_result['timestamp']}")
    
    if 'best_strategy_name' in optimization_result:
        print(f"  最佳策略: {optimization_result['best_strategy_name']}")
    
    # 显示参数对比
    display_parameter_comparison(current_params, best_params)
    
    # 确认应用
    print(f"\n⚠️  注意: 此操作将修改 src/models/improved_predictor.py 文件")
    print(f"原文件将备份为 .backup 文件")
    
    confirm = input("\n是否应用这些参数? (y/n): ").strip().lower()
    
    if confirm == 'y':
        try:
            update_improved_predictor_parameters(best_params)
            print("\n🎉 参数应用成功!")
            print("\n建议:")
            print("1. 运行回测验证新参数的效果")
            print("2. 如果效果不佳，可以从备份文件恢复")
            
        except Exception as e:
            print(f"\n❌ 参数应用失败: {e}")
            
    else:
        print("\n❌ 已取消参数应用")


if __name__ == "__main__":
    main()
