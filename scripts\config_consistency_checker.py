#!/usr/bin/env python3
"""
配置一致性检查工具
扫描所有配置文件和代码，检查关键参数的一致性
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any
from datetime import datetime

class ConfigConsistencyChecker:
    """配置一致性检查器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.issues = []
        self.config_values = {}
        
        # 定义需要检查的关键配置项
        self.key_configs = {
            'backtest_periods': {
                'expected': 10,
                'patterns': [
                    r'num_periods.*?=.*?(\d+)',
                    r'default_periods.*?=.*?(\d+)',
                    r'BACKTEST_PERIODS.*?=.*?(\d+)',
                    r'test_periods.*?=.*?(\d+)',
                    r'backtest_periods.*?=.*?(\d+)',
                    r'periods.*?default=(\d+)',
                    r'run_backtest\(.*?num_periods=(\d+)',
                ]
            },
            'display_periods': {
                'expected': 10,
                'patterns': [
                    r'display_periods.*?=.*?(\d+)',
                    r'DISPLAY_PERIODS.*?=.*?(\d+)',
                    r'display.*?default=(\d+)',
                    r'display_periods=(\d+)',
                ]
            },
            'min_train_periods': {
                'expected': 0,
                'patterns': [
                    r'min_train_periods.*?=.*?(\d+)',
                    r'min_training_periods.*?=.*?(\d+)',
                    r'min_train_periods=(\d+)',
                ]
            }
        }
        
        # 定义要检查的文件类型和路径
        self.check_patterns = [
            "*.py",
            "*.md", 
            "*.env*",
            "*.json",
            "*.yaml",
            "*.yml"
        ]
        
        # 排除的目录
        self.exclude_dirs = {
            '.git', '__pycache__', '.pytest_cache', 'node_modules',
            '.venv', 'venv', 'env', 'logs', 'backup', 'backup_original'
        }

        # 排除的文件（避免检查工具自身和报告文件）
        self.exclude_files = {
            'config_consistency_checker.py',
            'auto_fix_config.py',
            'config_manager.py'
        }

        # 排除的文件模式
        self.exclude_patterns = [
            r'config_consistency_report_.*\.md',
            r'config_consistency_data_.*\.json',
            r'.*\.backup$'
        ]

    def scan_files(self) -> List[Path]:
        """扫描所有需要检查的文件"""
        files = []

        for pattern in self.check_patterns:
            for file_path in self.project_root.rglob(pattern):
                # 跳过排除的目录
                if any(exclude_dir in file_path.parts for exclude_dir in self.exclude_dirs):
                    continue

                # 跳过排除的文件
                if file_path.name in self.exclude_files:
                    continue

                # 跳过匹配排除模式的文件
                if any(re.match(pattern, file_path.name) for pattern in self.exclude_patterns):
                    continue

                # 跳过二进制文件和大文件
                if file_path.stat().st_size > 1024 * 1024:  # 1MB
                    continue

                files.append(file_path)

        return files

    def _get_line_content(self, content: str, line_num: int) -> str:
        """获取指定行的内容"""
        lines = content.split('\n')
        if 1 <= line_num <= len(lines):
            return lines[line_num - 1]
        return ""

    def _should_skip_line(self, line_content: str, file_path: Path) -> bool:
        """判断是否应该跳过这一行"""
        line = line_content.strip()

        # 跳过注释行
        if line.startswith('#') or line.startswith('//'):
            return True

        # 跳过正则表达式模式
        if 'r\'' in line or 'r"' in line:
            return True

        # 跳过字符串中的数字（简单检查）
        if line.count('"') >= 2 or line.count("'") >= 2:
            return True

        # 跳过文档字符串
        if '"""' in line or "'''" in line:
            return True

        # 跳过明显的示例代码或文档
        if any(keyword in line.lower() for keyword in ['example', '示例', 'demo', '演示']):
            return True

        return False

    def check_file(self, file_path: Path) -> List[Dict]:
        """检查单个文件中的配置"""
        file_issues = []

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            for config_name, config_info in self.key_configs.items():
                expected_value = config_info['expected']
                patterns = config_info['patterns']

                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)

                    for match in matches:
                        found_value = int(match.group(1))
                        line_num = content[:match.start()].count('\n') + 1

                        # 获取匹配行的内容进行过滤
                        line_content = self._get_line_content(content, line_num)

                        # 跳过不应该检查的行
                        if self._should_skip_line(line_content, file_path):
                            continue

                        if found_value != expected_value:
                            file_issues.append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'line': line_num,
                                'config': config_name,
                                'expected': expected_value,
                                'found': found_value,
                                'pattern': pattern,
                                'context': self._get_line_context(content, line_num)
                            })

                        # 记录所有找到的值
                        key = f"{config_name}_{file_path.name}"
                        if key not in self.config_values:
                            self.config_values[key] = []
                        self.config_values[key].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'value': found_value
                        })

        except Exception as e:
            file_issues.append({
                'file': str(file_path.relative_to(self.project_root)),
                'error': f"文件读取错误: {str(e)}"
            })

        return file_issues

    def _get_line_context(self, content: str, line_num: int, context_lines: int = 2) -> str:
        """获取指定行的上下文"""
        lines = content.split('\n')
        start = max(0, line_num - context_lines - 1)
        end = min(len(lines), line_num + context_lines)
        
        context = []
        for i in range(start, end):
            marker = ">>> " if i == line_num - 1 else "    "
            context.append(f"{marker}{i+1:3d}: {lines[i]}")
        
        return '\n'.join(context)

    def check_consistency(self) -> Dict:
        """执行完整的一致性检查"""
        print("🔍 开始配置一致性检查...")
        print(f"项目根目录: {self.project_root.absolute()}")
        
        # 扫描文件
        files = self.scan_files()
        print(f"扫描到 {len(files)} 个文件")
        
        # 检查每个文件
        all_issues = []
        for file_path in files:
            file_issues = self.check_file(file_path)
            all_issues.extend(file_issues)
        
        # 分析结果
        result = {
            'timestamp': datetime.now().isoformat(),
            'total_files_scanned': len(files),
            'total_issues_found': len(all_issues),
            'issues': all_issues,
            'config_values': self.config_values,
            'summary': self._generate_summary(all_issues)
        }
        
        return result

    def _generate_summary(self, issues: List[Dict]) -> Dict:
        """生成检查结果摘要"""
        summary = {
            'issues_by_config': {},
            'issues_by_file': {},
            'critical_issues': [],
            'recommendations': []
        }
        
        # 按配置项分组
        for issue in issues:
            if 'config' in issue:
                config = issue['config']
                if config not in summary['issues_by_config']:
                    summary['issues_by_config'][config] = []
                summary['issues_by_config'][config].append(issue)
        
        # 按文件分组
        for issue in issues:
            file = issue['file']
            if file not in summary['issues_by_file']:
                summary['issues_by_file'][file] = []
            summary['issues_by_file'][file].append(issue)
        
        # 识别关键问题
        for config, config_issues in summary['issues_by_config'].items():
            if len(config_issues) > 1:
                summary['critical_issues'].append({
                    'type': 'multiple_inconsistent_values',
                    'config': config,
                    'count': len(config_issues),
                    'files': [issue['file'] for issue in config_issues]
                })
        
        # 生成建议
        if summary['issues_by_config']:
            summary['recommendations'].extend([
                "统一所有配置文件中的参数值",
                "建立单一配置源，其他地方引用该源",
                "添加配置验证机制",
                "建立配置修改检查清单"
            ])
        
        return summary

    def generate_report(self, result: Dict, output_file: str = None) -> str:
        """生成详细报告"""
        report = []
        report.append("# 配置一致性检查报告")
        report.append(f"**检查时间**: {result['timestamp']}")
        report.append(f"**扫描文件数**: {result['total_files_scanned']}")
        report.append(f"**发现问题数**: {result['total_issues_found']}")
        report.append("")
        
        if result['total_issues_found'] == 0:
            report.append("✅ **恭喜！未发现配置一致性问题**")
        else:
            report.append("## ❌ 发现的问题")
            report.append("")
            
            # 按配置项分组显示问题
            for config, issues in result['summary']['issues_by_config'].items():
                report.append(f"### {config}")
                report.append(f"**期望值**: {self.key_configs[config]['expected']}")
                report.append("")
                
                for issue in issues:
                    report.append(f"- **文件**: `{issue['file']}`")
                    report.append(f"  - **行号**: {issue['line']}")
                    report.append(f"  - **发现值**: {issue['found']}")
                    report.append(f"  - **上下文**:")
                    report.append("    ```")
                    for line in issue['context'].split('\n'):
                        report.append(f"    {line}")
                    report.append("    ```")
                    report.append("")
            
            # 关键问题
            if result['summary']['critical_issues']:
                report.append("## 🚨 关键问题")
                for critical in result['summary']['critical_issues']:
                    report.append(f"- **{critical['config']}**: 在 {critical['count']} 个文件中发现不一致的值")
                    report.append(f"  - 涉及文件: {', '.join(critical['files'])}")
                report.append("")
            
            # 建议
            if result['summary']['recommendations']:
                report.append("## 💡 修复建议")
                for rec in result['summary']['recommendations']:
                    report.append(f"- {rec}")
                report.append("")
        
        report_text = '\n'.join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📄 报告已保存到: {output_file}")
        
        return report_text

def main():
    """主函数"""
    checker = ConfigConsistencyChecker()
    
    print("🧪 配置一致性检查工具")
    print("=" * 60)
    
    # 执行检查
    result = checker.check_consistency()
    
    # 生成报告
    report_file = f"config_consistency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    report = checker.generate_report(result, report_file)
    
    # 显示摘要
    print("\n📊 检查结果摘要:")
    print(f"  扫描文件数: {result['total_files_scanned']}")
    print(f"  发现问题数: {result['total_issues_found']}")
    
    if result['total_issues_found'] > 0:
        print("\n❌ 发现配置不一致问题，请查看详细报告")
        print("🔧 建议立即修复这些问题以确保系统一致性")
    else:
        print("\n✅ 配置一致性检查通过！")
    
    # 保存详细结果
    result_file = f"config_consistency_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 详细数据已保存到: {result_file}")

if __name__ == "__main__":
    main()
