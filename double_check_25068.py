#!/usr/bin/env python3
"""
再次检查25068期的数据，看看是否有其他可能的解释
"""

import pandas as pd

def double_check_25068():
    """再次检查25068期的数据"""
    
    print("🔍 再次检查25068期的数据")
    print("=" * 50)
    
    # 读取数据
    df = pd.read_csv('data/raw/dlt_data.csv')
    
    # 查找25068期
    period_25068 = df[df['期号'] == 25068]
    
    if len(period_25068) == 0:
        print("❌ 未找到25068期数据")
        return
    
    row = period_25068.iloc[0]
    print(f"📊 25068期完整数据:")
    print(f"期号: {row['期号']}")
    print(f"红球1: {row['红球1']:02d}")
    print(f"红球2: {row['红球2']:02d}")
    print(f"红球3: {row['红球3']:02d}")
    print(f"红球4: {row['红球4']:02d}")
    print(f"红球5: {row['红球5']:02d}")
    print(f"蓝球1: {row['蓝球1']:02d}")
    print(f"蓝球2: {row['蓝球2']:02d}")
    
    red_balls = [row['红球1'], row['红球2'], row['红球3'], row['红球4'], row['红球5']]
    blue_balls = [row['蓝球1'], row['蓝球2']]
    
    print(f"\n📊 解析后的号码:")
    print(f"红球: {red_balls}")
    print(f"蓝球: {blue_balls}")
    
    # 检查是否有数据错误
    print(f"\n🔍 数据完整性检查:")
    print(f"红球数量: {len(red_balls)} (应该是5)")
    print(f"蓝球数量: {len(blue_balls)} (应该是2)")
    print(f"红球范围: {min(red_balls)}-{max(red_balls)} (应该在1-35)")
    print(f"蓝球范围: {min(blue_balls)}-{max(blue_balls)} (应该在1-12)")
    
    # 重新计算各种比例
    print(f"\n📊 重新计算比例:")
    
    # 红球奇偶比
    odd_red = [x for x in red_balls if x % 2 == 1]
    even_red = [x for x in red_balls if x % 2 == 0]
    print(f"红球奇偶比: {len(odd_red)}:{len(even_red)} (奇数{odd_red}, 偶数{even_red})")
    
    # 红球大小比 (1-18 vs 19-35)
    small_red = [x for x in red_balls if 1 <= x <= 18]
    big_red = [x for x in red_balls if 19 <= x <= 35]
    print(f"红球大小比: {len(small_red)}:{len(big_red)} (小号{small_red}, 大号{big_red})")
    
    # 蓝球大小比 (1-6 vs 7-12)
    small_blue = [x for x in blue_balls if 1 <= x <= 6]
    big_blue = [x for x in blue_balls if 7 <= x <= 12]
    print(f"蓝球大小比: {len(small_blue)}:{len(big_blue)} (小号{small_blue}, 大号{big_blue})")
    
    # 检查用户可能的期望
    print(f"\n🤔 用户期望分析:")
    print(f"用户说'25068应该都是2:3'")
    print(f"实际结果:")
    print(f"  红球奇偶比: {len(odd_red)}:{len(even_red)} {'✅' if f'{len(odd_red)}:{len(even_red)}' == '2:3' else '❌'}")
    print(f"  红球大小比: {len(small_red)}:{len(big_red)} {'✅' if f'{len(small_red)}:{len(big_red)}' == '2:3' else '❌'}")
    print(f"  蓝球大小比: {len(small_blue)}:{len(big_blue)} {'✅' if f'{len(small_blue)}:{len(big_blue)}' == '2:3' else '❌'}")
    
    # 检查是否有其他可能的2:3组合
    print(f"\n🔍 寻找可能的2:3组合:")
    
    # 尝试不同的红球分界线
    boundaries_to_test = [
        (17, "1-17 vs 18-35"),
        (16, "1-16 vs 17-35"),
        (15, "1-15 vs 16-35"),
        (19, "1-19 vs 20-35"),
        (20, "1-20 vs 21-35")
    ]
    
    for boundary, desc in boundaries_to_test:
        small = [x for x in red_balls if x <= boundary]
        big = [x for x in red_balls if x > boundary]
        ratio = f"{len(small)}:{len(big)}"
        print(f"  {desc}: {ratio} {'✅' if ratio == '2:3' else ''}")
        if ratio == '2:3':
            print(f"    小号: {small}")
            print(f"    大号: {big}")

if __name__ == "__main__":
    double_check_25068()
