"""
贝叶斯号码组合选择器
使用贝叶斯方法对多组预测号码进行评估和排序
"""

import numpy as np
from typing import List, Tuple, Dict
from collections import Counter


class BayesCombinationSelector:
    """贝叶斯号码组合选择器"""

    def __init__(self, config: Dict = None):
        self.is_initialized = False
        self.historical_data = None

        # 优化后的默认配置（基于参数优化结果）
        default_config = {
            'frequency_weight': 0.3157894736842105,      # 31.6% - 频率分析权重
            'pattern_weight': 0.2631578947368421,        # 26.3% - 模式识别权重
            'balance_weight': 0.10526315789473684,       # 10.5% - 平衡性权重
            'trend_weight': 0.05263157894736842,         # 5.3% - 趋势分析权重
            'kill_avoidance_weight': 0.2631578947368421, # 26.3% - 杀号避免权重
            'historical_window': 50,                     # 历史数据窗口
            'recent_trend_window': 5,                    # 近期趋势窗口
            'overlap_target_red': 0.4,                   # 红球重叠目标
            'overlap_target_blue': 0.5                   # 蓝球重叠目标
        }

        # 合并用户配置
        if config:
            default_config.update(config)

        self.config = default_config

        # 设置特征权重
        self.feature_weights = {
            'frequency_score': self.config['frequency_weight'],
            'pattern_score': self.config['pattern_weight'],
            'balance_score': self.config['balance_weight'],
            'trend_score': self.config['trend_weight'],
            'kill_avoidance_score': self.config['kill_avoidance_weight']
        }
        
    def initialize(self, historical_data: List[Tuple[List[int], List[int]]],
                   kill_numbers: Dict[str, List[List[int]]] = None):
        """
        初始化选择器（增强异常处理版）

        Args:
            historical_data: 历史开奖数据 [(红球, 蓝球), ...]
            kill_numbers: 杀号数据
        """
        try:
            # 输入验证
            if not isinstance(historical_data, list) or len(historical_data) == 0:
                raise ValueError("历史数据为空或格式错误")

            # 验证历史数据格式
            validated_data = []
            for i, item in enumerate(historical_data):
                try:
                    if (isinstance(item, tuple) and len(item) == 2 and
                        isinstance(item[0], list) and len(item[0]) == 5 and
                        isinstance(item[1], list) and len(item[1]) == 2):
                        # 验证号码范围
                        red_valid = all(isinstance(n, int) and 1 <= n <= 35 for n in item[0])
                        blue_valid = all(isinstance(n, int) and 1 <= n <= 12 for n in item[1])
                        if red_valid and blue_valid:
                            validated_data.append(item)
                except Exception:
                    continue  # 跳过无效数据

            if len(validated_data) < 3:  # 降低要求：至少需要3期数据
                raise ValueError(f"有效历史数据不足: {len(validated_data)}期")

            self.historical_data = validated_data

            # 验证杀号信息
            try:
                if kill_numbers and isinstance(kill_numbers, dict):
                    validated_kills = {}

                    # 验证红球杀号
                    red_kills = kill_numbers.get('red', [])
                    if isinstance(red_kills, list):
                        validated_red = []
                        for kill_list in red_kills:
                            if isinstance(kill_list, list):
                                valid_kills = [n for n in kill_list if isinstance(n, int) and 1 <= n <= 35]
                                if valid_kills:
                                    validated_red.append(valid_kills)
                        validated_kills['red'] = validated_red

                    # 验证蓝球杀号
                    blue_kills = kill_numbers.get('blue', [])
                    if isinstance(blue_kills, list):
                        validated_blue = []
                        for kill_list in blue_kills:
                            if isinstance(kill_list, list):
                                valid_kills = [n for n in kill_list if isinstance(n, int) and 1 <= n <= 12]
                                if valid_kills:
                                    validated_blue.append(valid_kills)
                        validated_kills['blue'] = validated_blue

                    self.kill_numbers = validated_kills
                else:
                    self.kill_numbers = {'red': [], 'blue': []}
            except Exception as e:
                print(f"    ⚠️ 杀号验证失败: {e}")
                self.kill_numbers = {'red': [], 'blue': []}

            # 计算历史统计（增强异常处理）
            try:
                self._calculate_historical_stats()
            except Exception as e:
                print(f"    ⚠️ 历史统计计算失败: {e}")
                # 设置默认统计值
                self.red_frequency = Counter()
                self.blue_frequency = Counter()
                self.red_odd_even_patterns = []
                self.red_size_patterns = []
                self.blue_size_patterns = []

            # 设置特征权重（使用优化后的配置）
            self.feature_weights = {
                'frequency_score': self.config['frequency_weight'],
                'pattern_score': self.config['pattern_weight'],
                'trend_score': self.config['trend_weight'],
                'balance_score': self.config['balance_weight'],
                'kill_avoidance_score': self.config['kill_avoidance_weight']
            }

            self.is_initialized = True

        except Exception as e:
            print(f"    ❌ 选择器初始化失败: {e}")
            # 设置最小可用状态
            self.historical_data = []
            self.kill_numbers = {'red': [], 'blue': []}
            self.red_frequency = Counter()
            self.blue_frequency = Counter()
            self.red_odd_even_patterns = []
            self.red_size_patterns = []
            self.blue_size_patterns = []
            self.feature_weights = {
                'frequency_score': 0.3157894736842105,      # 优化后的频率权重
                'pattern_score': 0.2631578947368421,        # 优化后的模式权重
                'trend_score': 0.05263157894736842,         # 优化后的趋势权重
                'balance_score': 0.10526315789473684,       # 优化后的平衡权重
                'kill_avoidance_score': 0.2631578947368421  # 优化后的杀号权重
            }
            self.is_initialized = False
            raise  # 重新抛出异常让调用者处理
        
    def _calculate_historical_stats(self):
        """计算历史统计信息"""
        if not self.historical_data:
            return

        # 使用配置的历史窗口大小
        window_size = min(self.config['historical_window'], len(self.historical_data))
        data_window = self.historical_data[-window_size:]  # 使用最近的数据

        # 红球频率统计
        self.red_frequency = Counter()
        self.blue_frequency = Counter()

        # 奇偶比、大小比统计
        self.red_odd_even_patterns = []
        self.red_size_patterns = []
        self.blue_size_patterns = []

        for red_balls, blue_balls in data_window:
            # 频率统计
            self.red_frequency.update(red_balls)
            self.blue_frequency.update(blue_balls)

            # 模式统计
            red_odd = sum(1 for x in red_balls if x % 2 == 1)
            red_even = len(red_balls) - red_odd
            self.red_odd_even_patterns.append((red_odd, red_even))

            red_small = sum(1 for x in red_balls if x <= 18)
            red_big = len(red_balls) - red_small
            self.red_size_patterns.append((red_small, red_big))

            blue_small = sum(1 for x in blue_balls if x <= 6)
            blue_big = len(blue_balls) - blue_small
            self.blue_size_patterns.append((blue_small, blue_big))
    
    def evaluate_combinations(self, combinations: List[Tuple[List[int], List[int]]]) -> List[Tuple[int, float, Dict[str, float]]]:
        """
        使用贝叶斯方法评估号码组合
        
        Args:
            combinations: 号码组合列表 [(红球, 蓝球), ...]
            
        Returns:
            List[Tuple[int, float, Dict[str, float]]]: [(组合索引, 总分, 各项得分), ...]
        """
        if not self.is_initialized:
            raise ValueError("选择器尚未初始化")
            
        results = []
        
        for i, (red_balls, blue_balls) in enumerate(combinations):
            # 计算各项得分
            scores = self._calculate_combination_scores(red_balls, blue_balls)
            
            # 贝叶斯加权融合
            total_score = self._bayesian_fusion(scores)
            
            results.append((i, total_score, scores))
        
        # 按总分排序
        results.sort(key=lambda x: x[1], reverse=True)
        
        return results
    
    def _calculate_combination_scores(self, red_balls: List[int], blue_balls: List[int]) -> Dict[str, float]:
        """计算单个组合的各项得分"""
        scores = {}
        
        # 1. 频率得分 - 基于历史出现频率
        scores['frequency_score'] = self._calculate_frequency_score(red_balls, blue_balls)
        
        # 2. 模式得分 - 基于奇偶比、大小比的历史分布
        scores['pattern_score'] = self._calculate_pattern_score(red_balls, blue_balls)
        
        # 3. 平衡性得分 - 号码分布的均匀性
        scores['balance_score'] = self._calculate_balance_score(red_balls, blue_balls)
        
        # 4. 趋势得分 - 与最近期号码的关联性
        scores['trend_score'] = self._calculate_trend_score(red_balls, blue_balls)
        
        # 5. 杀号规避得分 - 避开杀号的程度
        scores['kill_avoidance_score'] = self._calculate_kill_avoidance_score(red_balls, blue_balls)
        
        return scores
    
    def _calculate_frequency_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算频率得分"""
        if not self.historical_data:
            return 0.5
            
        total_periods = len(self.historical_data)
        
        # 红球频率得分
        red_score = 0
        for num in red_balls:
            frequency = self.red_frequency.get(num, 0)
            # 使用对数平滑，避免极端值
            normalized_freq = (frequency + 1) / (total_periods + 35)
            red_score += np.log(normalized_freq + 0.001)
        
        # 蓝球频率得分
        blue_score = 0
        for num in blue_balls:
            frequency = self.blue_frequency.get(num, 0)
            normalized_freq = (frequency + 1) / (total_periods + 12)
            blue_score += np.log(normalized_freq + 0.001)
        
        # 归一化到0-1范围
        combined_score = (red_score + blue_score) / (len(red_balls) + len(blue_balls))
        return max(0, min(1, (combined_score + 6) / 6))  # 调整范围
    
    def _calculate_pattern_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算模式得分"""
        if not self.red_odd_even_patterns:
            return 0.5
            
        # 当前组合的模式
        red_odd = sum(1 for x in red_balls if x % 2 == 1)
        red_even = len(red_balls) - red_odd
        
        red_small = sum(1 for x in red_balls if x <= 18)
        red_big = len(red_balls) - red_small
        
        blue_small = sum(1 for x in blue_balls if x <= 6)
        blue_big = len(blue_balls) - blue_small
        
        # 计算与历史模式的匹配度
        odd_even_matches = sum(1 for pattern in self.red_odd_even_patterns 
                              if pattern == (red_odd, red_even))
        size_matches = sum(1 for pattern in self.red_size_patterns 
                          if pattern == (red_small, red_big))
        blue_size_matches = sum(1 for pattern in self.blue_size_patterns 
                               if pattern == (blue_small, blue_big))
        
        total_periods = len(self.historical_data)
        
        # 计算概率
        odd_even_prob = (odd_even_matches + 1) / (total_periods + 6)  # 平滑
        size_prob = (size_matches + 1) / (total_periods + 6)
        blue_size_prob = (blue_size_matches + 1) / (total_periods + 3)
        
        # 综合得分
        return (odd_even_prob + size_prob + blue_size_prob) / 3
    
    def _calculate_balance_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算平衡性得分"""
        # 红球分布平衡性
        red_ranges = [0, 0, 0, 0, 0]  # 1-7, 8-14, 15-21, 22-28, 29-35
        for num in red_balls:
            range_idx = min(4, (num - 1) // 7)
            red_ranges[range_idx] += 1
        
        # 计算红球分布的标准差（越小越平衡）
        red_std = np.std(red_ranges)
        red_balance = max(0, 1 - red_std / 2.5)  # 归一化
        
        # 蓝球分布平衡性
        blue_ranges = [0, 0]  # 1-6, 7-12
        for num in blue_balls:
            range_idx = 0 if num <= 6 else 1
            blue_ranges[range_idx] += 1
        
        blue_balance = 1 - abs(blue_ranges[0] - blue_ranges[1]) / len(blue_balls)
        
        return (red_balance + blue_balance) / 2
    
    def _calculate_trend_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算趋势得分"""
        trend_window = self.config['recent_trend_window']
        if len(self.historical_data) < trend_window:
            return 0.5

        # 最近N期的号码
        recent_red = set()
        recent_blue = set()

        for red, blue in self.historical_data[-trend_window:]:
            recent_red.update(red)
            recent_blue.update(blue)

        # 计算与最近号码的重叠度
        red_overlap = len(set(red_balls) & recent_red) / len(red_balls)
        blue_overlap = len(set(blue_balls) & recent_blue) / len(blue_balls)

        # 使用配置的期望重叠度
        red_trend = 1 - abs(red_overlap - self.config['overlap_target_red'])
        blue_trend = 1 - abs(blue_overlap - self.config['overlap_target_blue'])

        return (red_trend + blue_trend) / 2
    
    def _calculate_kill_avoidance_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算杀号规避得分（增强异常处理版）"""
        try:
            # 输入验证
            if not isinstance(red_balls, list) or not isinstance(blue_balls, list):
                return 1.0

            if not self.kill_numbers or not isinstance(self.kill_numbers, dict):
                return 1.0

            total_penalty = 0
            total_kills = 0

            # 检查红球杀号（增强异常处理）
            try:
                red_kill_lists = self.kill_numbers.get('red', [])
                if isinstance(red_kill_lists, list):
                    for red_kill_list in red_kill_lists:
                        if isinstance(red_kill_list, list) and red_kill_list:
                            # 检查红球列表中是否包含任何杀号
                            for kill_num in red_kill_list:
                                if isinstance(kill_num, int) and kill_num in red_balls:
                                    total_penalty += 1
                            total_kills += len(red_kill_list)
            except Exception as e:
                print(f"    ⚠️ 红球杀号检查异常: {e}")

            # 检查蓝球杀号（增强异常处理）
            try:
                blue_kill_lists = self.kill_numbers.get('blue', [])
                if isinstance(blue_kill_lists, list):
                    for blue_kill_list in blue_kill_lists:
                        if isinstance(blue_kill_list, list) and blue_kill_list:
                            # 检查蓝球列表中是否包含任何杀号
                            for kill_num in blue_kill_list:
                                if isinstance(kill_num, int) and kill_num in blue_balls:
                                    total_penalty += 1
                            total_kills += len(blue_kill_list)
            except Exception as e:
                print(f"    ⚠️ 蓝球杀号检查异常: {e}")

            # 计算得分
            if total_kills == 0:
                return 1.0

            # 如果包含杀号，严重降低得分
            if total_penalty > 0:
                print(f"    ⚠️ 发现杀号冲突: 惩罚{total_penalty}个, 总杀号{total_kills}个")
                return 0.1  # 包含杀号的组合得分极低

            return 1.0  # 不包含杀号的组合得满分

        except Exception as e:
            print(f"    ❌ 杀号规避计算异常: {e}")
            return 1.0  # 异常时返回中性得分
    
    def _bayesian_fusion(self, scores: Dict[str, float]) -> float:
        """贝叶斯融合各项得分"""
        # 使用对数概率进行贝叶斯融合
        log_likelihood = 0
        
        for feature, score in scores.items():
            weight = self.feature_weights.get(feature, 0.2)
            # 将得分转换为对数似然
            likelihood = max(0.001, min(0.999, score))  # 避免极端值
            log_likelihood += weight * np.log(likelihood / (1 - likelihood))
        
        # 转换回概率
        exp_log = np.exp(log_likelihood)
        probability = exp_log / (1 + exp_log)
        
        return probability
    
    def select_top_combinations(self, combinations: List[Tuple[List[int], List[int]]],
                               top_k: int = 5) -> List[Dict]:
        """
        选择前K个最优组合（增强异常处理版）

        Args:
            combinations: 号码组合列表
            top_k: 返回前K个组合

        Returns:
            List[Dict]: 排序后的组合信息
        """
        try:
            # 输入验证
            if not isinstance(combinations, list) or len(combinations) == 0:
                raise ValueError("组合列表为空或格式错误")

            if not self.is_initialized:
                raise ValueError("选择器尚未初始化")

            top_k = max(1, min(len(combinations), int(top_k)))  # 限制范围

            # 评估组合（增强异常处理）
            try:
                evaluation_results = self.evaluate_combinations(combinations)
                if not evaluation_results:
                    raise ValueError("评估结果为空")
            except Exception as e:
                print(f"    ⚠️ 组合评估失败: {e}")
                # 使用简单排序作为回退
                evaluation_results = [(i, 0.5, {}) for i in range(len(combinations))]

            top_combinations = []
            try:
                for i, (combo_idx, total_score, scores) in enumerate(evaluation_results[:top_k]):
                    try:
                        # 验证组合索引
                        if combo_idx >= len(combinations):
                            continue

                        red_balls, blue_balls = combinations[combo_idx]

                        # 验证组合数据
                        if (not isinstance(red_balls, list) or len(red_balls) != 5 or
                            not isinstance(blue_balls, list) or len(blue_balls) != 2):
                            continue

                        combination_info = {
                            'rank': i + 1,
                            'original_index': combo_idx + 1,
                            'red_balls': sorted(red_balls),
                            'blue_balls': sorted(blue_balls),
                            'total_score': max(0.0, min(1.0, float(total_score))),
                            'confidence': min(100, max(0, int(total_score * 100))),
                            'scores': scores if isinstance(scores, dict) else {},
                            'recommendation': self._get_recommendation_level(total_score)
                        }

                        top_combinations.append(combination_info)

                    except Exception as e:
                        print(f"    ⚠️ 处理组合{combo_idx}失败: {e}")
                        continue

                if not top_combinations:
                    # 最终回退：返回第一个有效组合
                    for i, (red, blue) in enumerate(combinations[:top_k]):
                        if (isinstance(red, list) and len(red) == 5 and
                            isinstance(blue, list) and len(blue) == 2):
                            top_combinations.append({
                                'rank': len(top_combinations) + 1,
                                'original_index': i + 1,
                                'red_balls': sorted(red),
                                'blue_balls': sorted(blue),
                                'total_score': 0.3,
                                'confidence': 30,
                                'scores': {'fallback': 0.3},
                                'recommendation': "⚠️回退"
                            })
                            break

                return top_combinations

            except Exception as e:
                print(f"    ⚠️ 组合处理失败: {e}")
                return self._get_emergency_combinations(combinations, top_k)

        except Exception as e:
            print(f"    ❌ 选择组合失败: {e}")
            return self._get_emergency_combinations(combinations, top_k)

    def _get_emergency_combinations(self, combinations: List[Tuple[List[int], List[int]]],
                                   top_k: int) -> List[Dict]:
        """紧急回退组合生成"""
        try:
            emergency_combinations = []
            for i, (red, blue) in enumerate(combinations[:top_k]):
                try:
                    if (isinstance(red, list) and isinstance(blue, list) and
                        len(red) == 5 and len(blue) == 2):
                        emergency_combinations.append({
                            'rank': i + 1,
                            'original_index': i + 1,
                            'red_balls': sorted(red),
                            'blue_balls': sorted(blue),
                            'total_score': 0.2,
                            'confidence': 20,
                            'scores': {'emergency': 0.2},
                            'recommendation': "❌紧急"
                        })
                except Exception:
                    continue

            if not emergency_combinations:
                # 最终兜底
                emergency_combinations = [{
                    'rank': 1,
                    'original_index': 1,
                    'red_balls': [1, 2, 3, 4, 5],
                    'blue_balls': [1, 2],
                    'total_score': 0.1,
                    'confidence': 10,
                    'scores': {'critical': 0.1},
                    'recommendation': "❌严重"
                }]

            return emergency_combinations
        except Exception:
            return [{
                'rank': 1,
                'original_index': 1,
                'red_balls': [1, 2, 3, 4, 5],
                'blue_balls': [1, 2],
                'total_score': 0.1,
                'confidence': 10,
                'scores': {'fatal': 0.1},
                'recommendation': "❌致命"
            }]
    
    def _get_recommendation_level(self, score: float) -> str:
        """根据得分获取推荐等级"""
        if score >= 0.8:
            return "🌟强烈推荐"
        elif score >= 0.7:
            return "⭐推荐"
        elif score >= 0.6:
            return "✅可选"
        elif score >= 0.5:
            return "⚠️谨慎"
        else:
            return "❌不推荐"
