#!/usr/bin/env python3
"""
项目结构清理脚本

将剩余的文件移动到正确的目录结构中。
"""

import os
import shutil
import glob
from pathlib import Path

def cleanup_project():
    """清理项目结构"""
    print("🧹 开始清理项目结构...")
    
    # 获取项目根目录
    root_dir = Path(__file__).parent
    
    # 要移动的文件模式和目标目录
    file_moves = [
        # 测试文件 -> tests/
        ("test_*.py", "tests/"),
        ("debug_*.py", "tests/"),
        ("check_*.py", "tests/"),
        ("verify_*.py", "tests/"),
        
        # 临时脚本 -> scripts/
        ("demo_*.py", "scripts/"),
        ("fixed_*.py", "scripts/"),
        ("find_*.py", "scripts/"),
        ("double_*.py", "scripts/"),
        ("backtest_*.py", "scripts/"),
        ("optimize_*.py", "scripts/"),
    ]
    
    # 执行文件移动
    for pattern, target_dir in file_moves:
        target_path = root_dir / target_dir
        target_path.mkdir(exist_ok=True)
        
        files = glob.glob(str(root_dir / pattern))
        for file_path in files:
            file_name = os.path.basename(file_path)
            target_file = target_path / file_name
            
            try:
                if os.path.exists(file_path):
                    shutil.move(file_path, target_file)
                    print(f"✅ 移动: {file_name} -> {target_dir}")
            except Exception as e:
                print(f"❌ 移动失败: {file_name} - {e}")
    
    # 删除重复的原始文件
    original_files = [
        "main.py",  # 保留main_entry.py
        "advanced_probabilistic_system.py",
        "optimize_bayes_parameters.py", 
        "optimize_predictor_parameters_unified.py",
        "blue_ball_analysis_system.py"
    ]
    
    for file_name in original_files:
        file_path = root_dir / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"🗑️  删除重复文件: {file_name}")
            except Exception as e:
                print(f"❌ 删除失败: {file_name} - {e}")
    
    print("✅ 项目结构清理完成！")
    
    # 显示清理后的结构
    print("\n📁 清理后的项目结构:")
    show_structure(root_dir)

def show_structure(path, prefix="", max_depth=2, current_depth=0):
    """显示目录结构"""
    if current_depth >= max_depth:
        return
        
    items = sorted(path.iterdir())
    dirs = [item for item in items if item.is_dir() and not item.name.startswith('.')]
    files = [item for item in items if item.is_file() and not item.name.startswith('.')]
    
    # 显示目录
    for i, item in enumerate(dirs):
        is_last = i == len(dirs) - 1 and len(files) == 0
        print(f"{prefix}{'└── ' if is_last else '├── '}{item.name}/")
        extension = "    " if is_last else "│   "
        show_structure(item, prefix + extension, max_depth, current_depth + 1)
    
    # 显示重要文件
    important_files = [f for f in files if f.suffix in ['.py', '.md', '.txt', '.json'] and f.name in [
        'main_entry.py', 'optimize_entry.py', 'README.md', 'PROJECT_STRUCTURE.md', 
        'requirements.txt', 'setup.py'
    ]]
    
    for i, item in enumerate(important_files):
        is_last = i == len(important_files) - 1
        print(f"{prefix}{'└── ' if is_last else '├── '}{item.name}")

if __name__ == "__main__":
    cleanup_project()
