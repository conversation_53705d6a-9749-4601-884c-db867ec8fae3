"""
统一配置管理器
提供系统级别的配置管理，确保所有组件使用一致的配置
"""

from dataclasses import dataclass
from typing import Dict, Any
import os

@dataclass
class StandardBacktestConfig:
    """标准回测配置"""
    num_periods: int = 10
    min_train_periods: int = 0
    display_periods: int = 10
    
    @classmethod
    def from_env(cls):
        """从环境变量创建配置"""
        return cls(
            num_periods=int(os.getenv('BACKTEST_PERIODS', 10)),
            min_train_periods=int(os.getenv('MIN_TRAIN_PERIODS', 0)),
            display_periods=int(os.getenv('DISPLAY_PERIODS', 10))
        )

class ConfigManager:
    """统一配置管理器"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_backtest_config(cls) -> StandardBacktestConfig:
        """获取标准回测配置"""
        if cls._config is None:
            cls._config = StandardBacktestConfig.from_env()
        return cls._config
    
    @classmethod
    def get_kill_config(cls) -> Dict[str, int]:
        """获取杀号配置"""
        return {
            'red_count': int(os.getenv('RED_KILL_COUNT', 5)),
            'blue_count': int(os.getenv('BLUE_KILL_COUNT', 2))
        }
    
    @classmethod
    def validate_consistency(cls) -> bool:
        """验证配置一致性"""
        config = cls.get_backtest_config()
        
        # 基本验证
        if config.num_periods <= 0:
            return False
        if config.min_train_periods < 0:
            return False
        if config.display_periods < 0:
            return False
            
        return True
    
    @classmethod
    def get_all_configs(cls) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            'backtest': cls.get_backtest_config(),
            'kill': cls.get_kill_config(),
            'is_valid': cls.validate_consistency()
        }

# 全局配置实例
config_manager = ConfigManager()

# 便捷函数
def get_standard_backtest_config():
    """获取标准回测配置的便捷函数"""
    return config_manager.get_backtest_config()

def validate_system_config():
    """验证系统配置的便捷函数"""
    if not config_manager.validate_consistency():
        raise ValueError("系统配置不一致，请检查配置文件")
    return True
