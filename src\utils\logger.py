"""
彩票预测系统日志模块
提供统一的日志输出格式和管理
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path


class LotteryLogger:
    """彩票预测系统专用日志器"""

    def __init__(self, log_dir: str = "logs"):
        """
        初始化日志器

        Args:
            log_dir: 日志目录
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)

        # 创建今日日志文件
        today = datetime.now().strftime("%Y%m%d")
        self.log_file = self.log_dir / f"lottery_prediction_{today}.log"

    def log_prediction_result(self, period: str, target_period: str, target_index: int,
                            predictions: Dict, actual_result: Dict,
                            kill_numbers: Dict, bayes_combinations: List[Dict]):
        """
        记录预测结果（实时输出格式）

        Args:
            period: 基于期号
            target_period: 预测目标期号
            target_index: 目标索引
            predictions: 预测结果
            actual_result: 实际开奖结果
            kill_numbers: 杀号结果
            bayes_combinations: 贝叶斯推荐组合
        """
        # 构建输出内容
        output_lines = []

        # 标题行
        output_lines.append(f"基于期号 {period} 预判 {target_period}(索引 {target_index}):")

        # 红球预测结果
        output_lines.append("\t红球")

        # 红球奇偶比
        red_odd_even_pred = predictions.get('red_odd_even', [])
        if red_odd_even_pred and len(red_odd_even_pred) > 0:
            pred_ratio, pred_prob = red_odd_even_pred[0]
            actual_ratio = actual_result.get('red_odd_even', 'Unknown')
            status = "✅" if pred_ratio == actual_ratio else "❌"
            output_lines.append(f"\t\t奇偶比: 预测[{pred_ratio}({pred_prob:.3f})] -> 实际[{actual_ratio}] {status}")

        # 红球大小比
        red_size_pred = predictions.get('red_size', [])
        if red_size_pred and len(red_size_pred) > 0:
            pred_ratio, pred_prob = red_size_pred[0]
            actual_ratio = actual_result.get('red_size', 'Unknown')
            status = "✅" if pred_ratio == actual_ratio else "❌"
            output_lines.append(f"\t\t大小比: 预测[{pred_ratio}({pred_prob:.3f})] -> 实际[{actual_ratio}] {status}")

        # 蓝球预测结果
        output_lines.append("\t蓝球")

        # 蓝球大小比
        blue_size_pred = predictions.get('blue_size', [])
        if blue_size_pred and len(blue_size_pred) > 0:
            pred_ratio, pred_prob = blue_size_pred[0]
            actual_ratio = actual_result.get('blue_size', 'Unknown')
            status = "✅" if pred_ratio == actual_ratio else "❌"
            output_lines.append(f"\t大小比: 预测[{pred_ratio}({pred_prob:.3f})] -> 实际[{actual_ratio}] {status}")

        # 杀号结果
        if kill_numbers:
            red_kills = kill_numbers.get('red_universal', [])
            blue_kills = kill_numbers.get('blue_universal', [])
            actual_red = actual_result.get('red_balls', [])
            actual_blue = actual_result.get('blue_balls', [])

            # 红球杀号
            if red_kills:
                red_kill_success = not any(kill in actual_red for kill in red_kills)
                red_status = "✅" if red_kill_success else "❌"
                red_kill_str = ','.join([f'{n:02d}' for n in red_kills])
                output_lines.append(f"\t红球杀号：({red_kill_str}) {red_status}")

            # 蓝球杀号
            if blue_kills:
                blue_kill_success = not any(kill in actual_blue for kill in blue_kills)
                blue_status = "✅" if blue_kill_success else "❌"
                blue_kill_str = ','.join([f'{n:02d}' for n in blue_kills])
                output_lines.append(f"\t蓝球杀号：({blue_kill_str}) {blue_status}")

        output_lines.append("")

        # 贝叶斯推荐组合
        if bayes_combinations:
            output_lines.append("\t\t贝叶斯推荐组合 (前10组):")

            actual_red = set(actual_result.get('red_balls', []))
            actual_blue = set(actual_result.get('blue_balls', []))

            for combo in bayes_combinations:
                rank = combo.get('rank', 0)
                red_balls = combo.get('red_balls', [])
                blue_balls = combo.get('blue_balls', [])
                confidence = combo.get('confidence', 0)

                # 计算命中数
                red_hits = len(set(red_balls) & actual_red)
                blue_hits = len(set(blue_balls) & actual_blue)

                # 格式化号码
                red_str = ','.join([f'{n:02d}' for n in sorted(red_balls)])
                blue_str = ','.join([f'{n:02d}' for n in sorted(blue_balls)])

                output_lines.append(f"\t\t  第{rank}名：{red_str}——{blue_str} ({confidence:.0f}%)    命中（{red_hits}+{blue_hits}）")

        # 实际开奖号码
        actual_red = actual_result.get('red_balls', [])
        actual_blue = actual_result.get('blue_balls', [])
        if actual_red and actual_blue:
            red_str = ','.join([f'{n:02d}' for n in sorted(actual_red)])
            blue_str = ','.join([f'{n:02d}' for n in sorted(actual_blue)])
            output_lines.append(f"实际开奖号码：{red_str}——{blue_str}")

        # 输出到控制台
        result_text = '\n'.join(output_lines)
        print(result_text)
        print()  # 空行分隔

        # 写入日志文件
        self._write_to_file(result_text)

    def log_backtest_start(self, total_periods: int, config: str):
        """记录回测开始"""
        message = f"🧪 开始回测 - {config}"
        print(message)
        self._write_to_file(message)

        message = f"📊 配置：{total_periods}期回测"
        print(message)
        self._write_to_file(message)
        print("=" * 60)
        self._write_to_file("=" * 60)

    def log_backtest_summary(self, stats: Dict):
        """记录回测总结"""
        print("=" * 60)
        print("📊 统计信息")
        print("=" * 60)

        summary_lines = [
            "=" * 60,
            "📊 统计信息",
            "=" * 60
        ]

        # 基本统计
        total_periods = stats.get('total_periods', 0)
        processed_periods = stats.get('processed_periods', 0)

        summary_lines.append(f"总回测期数: {total_periods}")
        summary_lines.append(f"成功处理期数: {processed_periods}")
        summary_lines.append("")

        print(f"总回测期数: {total_periods}")
        print(f"成功处理期数: {processed_periods}")
        print()

        # 各项指标表现
        print("📈 各项指标表现:")
        summary_lines.append("📈 各项指标表现:")

        metrics = [
            ('红球奇偶比命中率', 'red_odd_even_accuracy'),
            ('红球大小比命中率', 'red_size_accuracy'),
            ('蓝球大小比命中率', 'blue_size_accuracy'),
            ('2+1命中率', 'hit_2_1_rate'),
            ('红球杀号成功率', 'red_kill_success_rate'),
            ('蓝球杀号成功率', 'blue_kill_success_rate')
        ]

        for name, key in metrics:
            if key in stats:
                rate = stats[key]
                count_key = key.replace('_rate', '_count').replace('_accuracy', '_count')
                count = stats.get(count_key, 0)

                emoji = "🎯" if rate >= 0.6 else ""
                line = f"  {name}: {rate:.1%} ({count}/{total_periods}) {emoji}"
                print(line)
                summary_lines.append(line)

        # 写入文件
        for line in summary_lines:
            self._write_to_file(line)

    def log_progress(self, current: int, total: int, period: str, target_period: str):
        """记录进度"""
        message = f"回测期号 {period}："
        print(message)

        message = f"  🎯 预测目标：期号 {target_period}"
        print(message)

        message = f"  🔍 进度：{current}/{total}"
        print(message)

    def _write_to_file(self, content: str):
        """写入日志文件"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] {content}\n")
        except Exception as e:
            print(f"写入日志文件失败: {e}")


# 全局日志器实例
_logger_instance = None

def get_logger() -> LotteryLogger:
    """获取全局日志器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = LotteryLogger()
    return _logger_instance

def log_prediction_result(period: str, target_period: str, target_index: int,
                        predictions: Dict, actual_result: Dict,
                        kill_numbers: Dict, bayes_combinations: List[Dict]):
    """便捷函数：记录预测结果"""
    logger = get_logger()
    logger.log_prediction_result(period, target_period, target_index,
                               predictions, actual_result, kill_numbers, bayes_combinations)

def log_backtest_start(total_periods: int, config: str):
    """便捷函数：记录回测开始"""
    logger = get_logger()
    logger.log_backtest_start(total_periods, config)

def log_backtest_summary(stats: Dict):
    """便捷函数：记录回测总结"""
    logger = get_logger()
    logger.log_backtest_summary(stats)

def log_progress(current: int, total: int, period: str, target_period: str):
    """便捷函数：记录进度"""
    logger = get_logger()
    logger.log_progress(current, total, period, target_period)
