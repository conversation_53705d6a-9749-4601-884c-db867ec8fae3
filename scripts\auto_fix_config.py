#!/usr/bin/env python3
"""
自动修复配置不一致问题
针对最关键的文件进行自动修复
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple

class ConfigAutoFixer:
    """配置自动修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.fixes_applied = []
        
        # 定义标准配置值
        self.standard_values = {
            'num_periods': 10,
            'min_train_periods': 0,
            'display_periods': 10,
            'test_periods': 10,
            'backtest_periods': 10,
            'default_periods': 10,
        }
        
        # 定义需要修复的关键文件（按优先级排序）
        self.critical_files = [
            'advanced_probabilistic_system.py',
            'optimize_bayes_parameters.py', 
            'optimize_predictor_parameters_unified.py',
            'src/systems/main.py',
        ]
        
        # 定义修复模式
        self.fix_patterns = [
            # BacktestConfig构造函数中的参数
            (r'(min_train_periods\s*=\s*)\d+', r'\g<1>0'),
            (r'(display_periods\s*=\s*)\d+(?!\d)', r'\g<1>10'),  # 避免匹配100等
            (r'(num_periods\s*=\s*)\d+(?!\d)', r'\g<1>10'),
            (r'(test_periods\s*=\s*)\d+(?!\d)', r'\g<1>10'),
            
            # 函数参数默认值
            (r'(test_periods:\s*int\s*=\s*)\d+', r'\g<1>10'),
            (r'(num_periods:\s*int\s*=\s*)\d+', r'\g<1>10'),
            (r'(display_periods:\s*int\s*=\s*)\d+', r'\g<1>10'),
            
            # 变量赋值
            (r'(test_periods\s*=\s*)\d+', r'\g<1>10'),
            (r'(num_periods\s*=\s*)\d+', r'\g<1>10'),
        ]

    def fix_file(self, file_path: Path) -> List[Dict]:
        """修复单个文件"""
        fixes = []
        
        if not file_path.exists():
            return fixes
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用修复模式
            for pattern, replacement in self.fix_patterns:
                matches = list(re.finditer(pattern, content))
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    old_value = match.group(0)
                    
                    # 检查是否需要修复
                    if self._should_fix(old_value, pattern):
                        fixes.append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'old': old_value,
                            'pattern': pattern,
                            'replacement': replacement
                        })
                
                # 应用替换
                content = re.sub(pattern, replacement, content)
            
            # 如果有修改，写回文件
            if content != original_content:
                # 备份原文件
                backup_path = file_path.with_suffix(file_path.suffix + '.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # 写入修复后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 已修复: {file_path.relative_to(self.project_root)}")
                print(f"📁 备份保存到: {backup_path.relative_to(self.project_root)}")
            
        except Exception as e:
            fixes.append({
                'file': str(file_path.relative_to(self.project_root)),
                'error': f"修复失败: {str(e)}"
            })
        
        return fixes

    def _should_fix(self, matched_text: str, pattern: str) -> bool:
        """判断是否需要修复"""
        # 提取数值
        numbers = re.findall(r'\d+', matched_text)
        if not numbers:
            return False
        
        value = int(numbers[0])
        
        # 根据模式判断是否需要修复
        if 'min_train_periods' in matched_text:
            return value != 0
        elif any(key in matched_text for key in ['display_periods', 'num_periods', 'test_periods']):
            return value != 10
        
        return False

    def fix_critical_files(self) -> Dict:
        """修复关键文件"""
        print("🔧 开始自动修复关键配置文件...")
        
        all_fixes = []
        
        for file_name in self.critical_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                print(f"\n📝 修复文件: {file_name}")
                fixes = self.fix_file(file_path)
                all_fixes.extend(fixes)
                
                if fixes:
                    print(f"   应用了 {len(fixes)} 个修复")
                else:
                    print("   无需修复")
            else:
                print(f"⚠️ 文件不存在: {file_name}")
        
        return {
            'total_fixes': len(all_fixes),
            'fixes': all_fixes,
            'files_processed': len(self.critical_files)
        }

    def create_unified_config_manager(self):
        """创建统一配置管理器"""
        config_manager_code = '''"""
统一配置管理器
提供系统级别的配置管理，确保所有组件使用一致的配置
"""

from dataclasses import dataclass
from typing import Dict, Any
import os

@dataclass
class StandardBacktestConfig:
    """标准回测配置"""
    num_periods: int = 10
    min_train_periods: int = 0
    display_periods: int = 10
    
    @classmethod
    def from_env(cls):
        """从环境变量创建配置"""
        return cls(
            num_periods=int(os.getenv('BACKTEST_PERIODS', 10)),
            min_train_periods=int(os.getenv('MIN_TRAIN_PERIODS', 0)),
            display_periods=int(os.getenv('DISPLAY_PERIODS', 10))
        )

class ConfigManager:
    """统一配置管理器"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_backtest_config(cls) -> StandardBacktestConfig:
        """获取标准回测配置"""
        if cls._config is None:
            cls._config = StandardBacktestConfig.from_env()
        return cls._config
    
    @classmethod
    def get_kill_config(cls) -> Dict[str, int]:
        """获取杀号配置"""
        return {
            'red_count': int(os.getenv('RED_KILL_COUNT', 5)),
            'blue_count': int(os.getenv('BLUE_KILL_COUNT', 2))
        }
    
    @classmethod
    def validate_consistency(cls) -> bool:
        """验证配置一致性"""
        config = cls.get_backtest_config()
        
        # 基本验证
        if config.num_periods <= 0:
            return False
        if config.min_train_periods < 0:
            return False
        if config.display_periods < 0:
            return False
            
        return True
    
    @classmethod
    def get_all_configs(cls) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            'backtest': cls.get_backtest_config(),
            'kill': cls.get_kill_config(),
            'is_valid': cls.validate_consistency()
        }

# 全局配置实例
config_manager = ConfigManager()

# 便捷函数
def get_standard_backtest_config():
    """获取标准回测配置的便捷函数"""
    return config_manager.get_backtest_config()

def validate_system_config():
    """验证系统配置的便捷函数"""
    if not config_manager.validate_consistency():
        raise ValueError("系统配置不一致，请检查配置文件")
    return True
'''
        
        config_file = self.project_root / 'config_manager.py'
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_manager_code)
        
        print(f"✅ 创建统一配置管理器: {config_file.relative_to(self.project_root)}")

def main():
    """主函数"""
    print("🛠️ 配置自动修复工具")
    print("=" * 60)
    
    fixer = ConfigAutoFixer()
    
    # 修复关键文件
    result = fixer.fix_critical_files()
    
    print(f"\n📊 修复结果:")
    print(f"  处理文件数: {result['files_processed']}")
    print(f"  应用修复数: {result['total_fixes']}")
    
    if result['total_fixes'] > 0:
        print("\n✅ 关键配置文件修复完成")
        print("💡 建议运行配置一致性检查工具验证修复效果")
    else:
        print("\n✅ 关键文件无需修复")
    
    # 创建统一配置管理器
    print("\n🔧 创建统一配置管理器...")
    fixer.create_unified_config_manager()
    
    print("\n🎯 下一步建议:")
    print("1. 运行: python config_consistency_checker.py")
    print("2. 检查修复效果")
    print("3. 测试系统功能")
    print("4. 提交修复后的代码")

if __name__ == "__main__":
    main()
