"""
简单测试predict_for_period方法
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.systems.main import LotteryPredictor
import pandas as pd


def simple_test():
    """简单测试"""
    print("🔍 简单测试predict_for_period方法")
    print("=" * 60)
    
    try:
        # 创建预测器
        predictor = LotteryPredictor()
        print("✅ 预测器创建成功")
        
        # 测试predict_for_period方法
        print("🎯 测试predict_for_period方法...")
        result = predictor.predict_for_period(1496, predictor.data)
        print("✅ predict_for_period成功！")
        print(f"结果类型: {type(result)}")
        print(f"期号: {result.period_number}")
        print(f"红球奇偶预测: {result.red_odd_even_predictions}")
        print(f"红球大小预测: {result.red_size_predictions}")
        print(f"蓝球大小预测: {result.blue_size_predictions}")
        print(f"杀号信息: {result.kill_numbers}")
        print(f"贝叶斯组合: {result.bayes_combinations}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    simple_test()
