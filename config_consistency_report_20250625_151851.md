# 配置一致性检查报告
**检查时间**: 2025-06-25T15:18:51.430547
**扫描文件数**: 177
**发现问题数**: 210

## ❌ 发现的问题

### min_train_periods
**期望值**: 0

- **文件**: `advanced_probabilistic_system.py`
  - **行号**: 2723
  - **发现值**: 20
  - **上下文**:
    ```
        2721:             config = BacktestConfig(
        2722:                 num_periods=self.test_periods,
    >>> 2723:                 min_train_periods=20,
        2724:                 display_periods=10,
        2725:                 metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号
    ```

- **文件**: `advanced_probabilistic_system.py`
  - **行号**: 2723
  - **发现值**: 20
  - **上下文**:
    ```
        2721:             config = BacktestConfig(
        2722:                 num_periods=self.test_periods,
    >>> 2723:                 min_train_periods=20,
        2724:                 display_periods=10,
        2725:                 metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号
    ```

- **文件**: `check_framework_integrity.py`
  - **行号**: 240
  - **发现值**: 10
  - **上下文**:
    ```
        238:         config = BacktestConfig(
        239:             num_periods=1,
    >>> 240:             min_train_periods=10,
        241:             display_periods=1
        242:         )
    ```

- **文件**: `check_framework_integrity.py`
  - **行号**: 240
  - **发现值**: 10
  - **上下文**:
    ```
        238:         config = BacktestConfig(
        239:             num_periods=1,
    >>> 240:             min_train_periods=10,
        241:             display_periods=1
        242:         )
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 111
  - **发现值**: 10
  - **上下文**:
    ```
        109:         
        110:         # 测试配置创建
    >>> 111:         config = BacktestConfig(num_periods=1, min_train_periods=10)
        112:         if config.validate():
        113:             print("✅ 配置验证成功")
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 111
  - **发现值**: 10
  - **上下文**:
    ```
        109:         
        110:         # 测试配置创建
    >>> 111:         config = BacktestConfig(num_periods=1, min_train_periods=10)
        112:         if config.validate():
        113:             print("✅ 配置验证成功")
    ```

- **文件**: `test_corrected_backtest.py`
  - **行号**: 48
  - **发现值**: 100
  - **上下文**:
    ```
         46:     config = BacktestConfig(
         47:         num_periods=5,  # 只回测5期，快速验证
    >>>  48:         min_train_periods=100  # 最少100期训练数据
         49:     )
         50:     
    ```

- **文件**: `test_corrected_backtest.py`
  - **行号**: 48
  - **发现值**: 100
  - **上下文**:
    ```
         46:     config = BacktestConfig(
         47:         num_periods=5,  # 只回测5期，快速验证
    >>>  48:         min_train_periods=100  # 最少100期训练数据
         49:     )
         50:     
    ```

- **文件**: `test_corrected_backtest_order.py`
  - **行号**: 40
  - **发现值**: 50
  - **上下文**:
    ```
         38:     config = BacktestConfig(
         39:         num_periods=5,
    >>>  40:         min_train_periods=50
         41:     )
         42:     
    ```

- **文件**: `test_corrected_backtest_order.py`
  - **行号**: 40
  - **发现值**: 50
  - **上下文**:
    ```
         38:     config = BacktestConfig(
         39:         num_periods=5,
    >>>  40:         min_train_periods=50
         41:     )
         42:     
    ```

- **文件**: `test_corrected_final_order.py`
  - **行号**: 24
  - **发现值**: 50
  - **上下文**:
    ```
         22:     # 模拟回测配置
         23:     num_periods = 4
    >>>  24:     min_train_periods = 50
         25:     total_data_len = len(data)
         26:     
    ```

- **文件**: `test_time_series_logic.py`
  - **行号**: 51
  - **发现值**: 10
  - **上下文**:
    ```
         49:     config = BacktestConfig(
         50:         num_periods=3,  # 只测试3期
    >>>  51:         min_train_periods=10,  # 最少10期训练数据
         52:         display_periods=3,
         53:         enable_detailed_output=True,
    ```

- **文件**: `test_time_series_logic.py`
  - **行号**: 51
  - **发现值**: 10
  - **上下文**:
    ```
         49:     config = BacktestConfig(
         50:         num_periods=3,  # 只测试3期
    >>>  51:         min_train_periods=10,  # 最少10期训练数据
         52:         display_periods=3,
         53:         enable_detailed_output=True,
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 41
  - **发现值**: 50
  - **上下文**:
    ```
         39:         
         40:         # 测试配置验证
    >>>  41:         config = BacktestConfig(num_periods=10, min_train_periods=50)
         42:         assert config.validate() == True
         43:         
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 149
  - **发现值**: 5
  - **上下文**:
    ```
        147:         config = BacktestConfig(
        148:             num_periods=3,      # 只测试3期
    >>> 149:             min_train_periods=5, # 最少5期训练
        150:             display_periods=3
        151:         )
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 41
  - **发现值**: 50
  - **上下文**:
    ```
         39:         
         40:         # 测试配置验证
    >>>  41:         config = BacktestConfig(num_periods=10, min_train_periods=50)
         42:         assert config.validate() == True
         43:         
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 149
  - **发现值**: 5
  - **上下文**:
    ```
        147:         config = BacktestConfig(
        148:             num_periods=3,      # 只测试3期
    >>> 149:             min_train_periods=5, # 最少5期训练
        150:             display_periods=3
        151:         )
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 41
  - **发现值**: 50
  - **上下文**:
    ```
         39:         
         40:         # 测试配置验证
    >>>  41:         config = BacktestConfig(num_periods=10, min_train_periods=50)
         42:         assert config.validate() == True
         43:         
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 149
  - **发现值**: 5
  - **上下文**:
    ```
        147:         config = BacktestConfig(
        148:             num_periods=3,      # 只测试3期
    >>> 149:             min_train_periods=5, # 最少5期训练
        150:             display_periods=3
        151:         )
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 41
  - **发现值**: 50
  - **上下文**:
    ```
         39:         
         40:         # 测试配置验证
    >>>  41:         config = BacktestConfig(num_periods=10, min_train_periods=50)
         42:         assert config.validate() == True
         43:         
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 149
  - **发现值**: 5
  - **上下文**:
    ```
        147:         config = BacktestConfig(
        148:             num_periods=3,      # 只测试3期
    >>> 149:             min_train_periods=5, # 最少5期训练
        150:             display_periods=3
        151:         )
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 44
  - **发现值**: 50
  - **上下文**:
    ```
         42:         config = BacktestConfig(
         43:             num_periods=10,           # 回测10期
    >>>  44:             min_train_periods=50,     # 最少50期训练数据
         45:             display_periods=5,        # 显示最近5期
         46:             enable_detailed_output=True,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 102
  - **发现值**: 30
  - **上下文**:
    ```
        100:         config = BacktestConfig(
        101:             num_periods=10,
    >>> 102:             min_train_periods=30,
        103:             display_periods=5,
        104:             metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号指标
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 44
  - **发现值**: 50
  - **上下文**:
    ```
         42:         config = BacktestConfig(
         43:             num_periods=10,           # 回测10期
    >>>  44:             min_train_periods=50,     # 最少50期训练数据
         45:             display_periods=5,        # 显示最近5期
         46:             enable_detailed_output=True,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 102
  - **发现值**: 30
  - **上下文**:
    ```
        100:         config = BacktestConfig(
        101:             num_periods=10,
    >>> 102:             min_train_periods=30,
        103:             display_periods=5,
        104:             metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号指标
    ```

- **文件**: `src\framework\predictor_adapter.py`
  - **行号**: 402
  - **发现值**: 50
  - **上下文**:
    ```
        400:     config = BacktestConfig(
        401:         num_periods=10,
    >>> 402:         min_train_periods=50,
        403:         display_periods=5
        404:     )
    ```

- **文件**: `src\framework\predictor_adapter.py`
  - **行号**: 402
  - **发现值**: 50
  - **上下文**:
    ```
        400:     config = BacktestConfig(
        401:         num_periods=10,
    >>> 402:         min_train_periods=50,
        403:         display_periods=5
        404:     )
    ```

- **文件**: `src\systems\main.py`
  - **行号**: 1420
  - **发现值**: 10
  - **上下文**:
    ```
        1418: 
        1419:         # 使用所有历史数据到当前期作为训练集
    >>> 1420:         min_train_periods = 10   # 最少需要10期训练数据
        1421:         max_backtest = min(num_periods, len(self.data) - min_train_periods)
        1422:         print(f"将回测 {max_backtest} 期数据（使用所有历史数据到当前期作为训练集）")
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 263
  - **发现值**: 20
  - **上下文**:
    ```
        261:                     config = BacktestConfig(
        262:                         num_periods=num_periods,
    >>> 263:                         min_train_periods=20,
        264:                         display_periods=display_periods,
        265:                         enable_detailed_output=False,  # 简化输出
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 263
  - **发现值**: 20
  - **上下文**:
    ```
        261:                     config = BacktestConfig(
        262:                         num_periods=num_periods,
    >>> 263:                         min_train_periods=20,
        264:                         display_periods=display_periods,
        265:                         enable_detailed_output=False,  # 简化输出
    ```

- **文件**: `final_consistency_report.md`
  - **行号**: 54
  - **发现值**: 50
  - **上下文**:
    ```
         52: config = BacktestConfig(
         53:     num_periods=test_periods,
    >>>  54:     min_train_periods=50,  # 最少50期训练数据
         55:     ...
         56: )
    ```

- **文件**: `final_consistency_report.md`
  - **行号**: 71
  - **发现值**: 100
  - **上下文**:
    ```
         69: config = BacktestConfig(
         70:     num_periods=min(backtest_periods, 10),
    >>>  71:     min_train_periods=100,  # 确保有足够的训练数据（至少100期）
         72:     ...
         73: )
    ```

- **文件**: `final_consistency_report.md`
  - **行号**: 54
  - **发现值**: 50
  - **上下文**:
    ```
         52: config = BacktestConfig(
         53:     num_periods=test_periods,
    >>>  54:     min_train_periods=50,  # 最少50期训练数据
         55:     ...
         56: )
    ```

- **文件**: `final_consistency_report.md`
  - **行号**: 71
  - **发现值**: 100
  - **上下文**:
    ```
         69: config = BacktestConfig(
         70:     num_periods=min(backtest_periods, 10),
    >>>  71:     min_train_periods=100,  # 确保有足够的训练数据（至少100期）
         72:     ...
         73: )
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 72
  - **发现值**: 50
  - **上下文**:
    ```
         70:     config = BacktestConfig(
         71:         num_periods=num_periods,
    >>>  72:         min_train_periods=50,
         73:         display_periods=display_periods
         74:     )
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 114
  - **发现值**: 50
  - **上下文**:
    ```
        112:     config = BacktestConfig(
        113:         num_periods=num_periods,
    >>> 114:         min_train_periods=50,
        115:         display_periods=display_periods,
        116:         enable_detailed_output=True,
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 164
  - **发现值**: 30
  - **上下文**:
    ```
        162:         config = BacktestConfig(
        163:             num_periods=num_periods,
    >>> 164:             min_train_periods=30,
        165:             display_periods=5
        166:         )
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 206
  - **发现值**: 20
  - **上下文**:
    ```
        204:     config = BacktestConfig(
        205:         num_periods=30,
    >>> 206:         min_train_periods=20,
        207:         display_periods=10,
        208:         metrics=['red_kill_success', 'blue_kill_success']  # 只关注杀号
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 72
  - **发现值**: 50
  - **上下文**:
    ```
         70:     config = BacktestConfig(
         71:         num_periods=num_periods,
    >>>  72:         min_train_periods=50,
         73:         display_periods=display_periods
         74:     )
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 114
  - **发现值**: 50
  - **上下文**:
    ```
        112:     config = BacktestConfig(
        113:         num_periods=num_periods,
    >>> 114:         min_train_periods=50,
        115:         display_periods=display_periods,
        116:         enable_detailed_output=True,
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 164
  - **发现值**: 30
  - **上下文**:
    ```
        162:         config = BacktestConfig(
        163:             num_periods=num_periods,
    >>> 164:             min_train_periods=30,
        165:             display_periods=5
        166:         )
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 206
  - **发现值**: 20
  - **上下文**:
    ```
        204:     config = BacktestConfig(
        205:         num_periods=30,
    >>> 206:         min_train_periods=20,
        207:         display_periods=10,
        208:         metrics=['red_kill_success', 'blue_kill_success']  # 只关注杀号
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 72
  - **发现值**: 50
  - **上下文**:
    ```
         70: config = BacktestConfig(
         71:     num_periods=10,           # 回测期数
    >>>  72:     min_train_periods=50,     # 最少训练期数
         73:     display_periods=5         # 显示期数
         74: )
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 90
  - **发现值**: 50
  - **上下文**:
    ```
         88: config = BacktestConfig(
         89:     num_periods=10,                    # 回测期数
    >>>  90:     min_train_periods=50,              # 最少训练期数
         91:     display_periods=5,                 # 显示期数
         92:     metrics=[                          # 评估指标
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 72
  - **发现值**: 50
  - **上下文**:
    ```
         70: config = BacktestConfig(
         71:     num_periods=10,           # 回测期数
    >>>  72:     min_train_periods=50,     # 最少训练期数
         73:     display_periods=5         # 显示期数
         74: )
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 90
  - **发现值**: 50
  - **上下文**:
    ```
         88: config = BacktestConfig(
         89:     num_periods=10,                    # 回测期数
    >>>  90:     min_train_periods=50,              # 最少训练期数
         91:     display_periods=5,                 # 显示期数
         92:     metrics=[                          # 评估指标
    ```

### backtest_periods
**期望值**: 10

- **文件**: `backtest_6_algorithms.py`
  - **行号**: 30
  - **发现值**: 15
  - **上下文**:
    ```
         28:     
         29:     # 回测参数
    >>>  30:     backtest_periods = 15  # 回测期数
         31:     start_index = 200      # 从第200期开始回测
         32:     
    ```

- **文件**: `backtest_6_algorithms.py`
  - **行号**: 30
  - **发现值**: 15
  - **上下文**:
    ```
         28:     
         29:     # 回测参数
    >>>  30:     backtest_periods = 15  # 回测期数
         31:     start_index = 200      # 从第200期开始回测
         32:     
    ```

- **文件**: `backtest_6_algorithms.py`
  - **行号**: 30
  - **发现值**: 15
  - **上下文**:
    ```
         28:     
         29:     # 回测参数
    >>>  30:     backtest_periods = 15  # 回测期数
         31:     start_index = 200      # 从第200期开始回测
         32:     
    ```

- **文件**: `blue_ball_analysis_system.py`
  - **行号**: 268
  - **发现值**: 30
  - **上下文**:
    ```
        266:         self.data = None
        267:         self.ensemble_system = None
    >>> 268:         self.test_periods = 30
        269:         self.kill_count_range = range(1, 11)  # 测试1-10个蓝球杀号
        270:         
    ```

- **文件**: `check_framework_integrity.py`
  - **行号**: 239
  - **发现值**: 1
  - **上下文**:
    ```
        237:         # 创建测试配置
        238:         config = BacktestConfig(
    >>> 239:             num_periods=1,
        240:             min_train_periods=10,
        241:             display_periods=1
    ```

- **文件**: `debug_actual_backtest.py`
  - **行号**: 29
  - **发现值**: 3
  - **上下文**:
    ```
         27:     
         28:     # 只测试最近3期
    >>>  29:     test_periods = 3
         30:     
         31:     for i in range(test_periods):
    ```

- **文件**: `debug_bayes_selection.py`
  - **行号**: 173
  - **发现值**: 3
  - **上下文**:
    ```
        171:     # 评估参数
        172:     try:
    >>> 173:         score = optimizer._evaluate_parameters(test_params, backtest_periods=3)
        174:         print(f"✅ 评估成功，得分: {score}")
        175:         
    ```

- **文件**: `debug_bayes_selection.py`
  - **行号**: 173
  - **发现值**: 3
  - **上下文**:
    ```
        171:     # 评估参数
        172:     try:
    >>> 173:         score = optimizer._evaluate_parameters(test_params, backtest_periods=3)
        174:         print(f"✅ 评估成功，得分: {score}")
        175:         
    ```

- **文件**: `debug_bayes_selection.py`
  - **行号**: 173
  - **发现值**: 3
  - **上下文**:
    ```
        171:     # 评估参数
        172:     try:
    >>> 173:         score = optimizer._evaluate_parameters(test_params, backtest_periods=3)
        174:         print(f"✅ 评估成功，得分: {score}")
        175:         
    ```

- **文件**: `debug_blue_ball_prediction.py`
  - **行号**: 59
  - **发现值**: 5
  - **上下文**:
    ```
         57:     
         58:     # 测试最近几期的预测
    >>>  59:     test_periods = 5
         60:     
         61:     for i in range(test_periods):
    ```

- **文件**: `demo_parameter_optimization.py`
  - **行号**: 27
  - **发现值**: 50
  - **上下文**:
    ```
         25: 
         26: 
    >>>  27: def evaluate_parameter_set(params: Dict, test_periods: int = 50) -> Dict:
         28:     """评估参数组合的性能"""
         29:     print(f"📊 评估参数组合...")
    ```

- **文件**: `fixed_parameter_optimization.py`
  - **行号**: 50
  - **发现值**: 80
  - **上下文**:
    ```
         48:         }
         49:     
    >>>  50:     def evaluate_parameters_robust(self, params: Dict, test_periods: int = 80) -> Dict:
         51:         """
         52:         稳健的参数评估方法
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 134
  - **发现值**: 20
  - **上下文**:
    ```
        132:             return False
        133: 
    >>> 134:     def evaluate_parameters(self, params: Dict[str, float], backtest_periods: int = 20) -> float:
        135:         """评估参数组合的性能（增强版）"""
        136:         try:
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 383
  - **发现值**: 15
  - **上下文**:
    ```
        381:         return total_score / valid_periods if valid_periods > 0 else 0.0
        382:     
    >>> 383:     def optimize(self, max_combinations: int = 500, backtest_periods: int = 15) -> Dict:
        384:         """执行参数优化（改进版）"""
        385:         print(f"🚀 开始贝叶斯参数优化")
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 480
  - **发现值**: 20
  - **上下文**:
    ```
        478: 
        479:         # 使用完整的参数设置进行优化
    >>> 480:         result = optimizer.optimize(max_combinations=1000, backtest_periods=20)
        481: 
        482:         if result and 'best_score' in result:
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 134
  - **发现值**: 20
  - **上下文**:
    ```
        132:             return False
        133: 
    >>> 134:     def evaluate_parameters(self, params: Dict[str, float], backtest_periods: int = 20) -> float:
        135:         """评估参数组合的性能（增强版）"""
        136:         try:
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 383
  - **发现值**: 15
  - **上下文**:
    ```
        381:         return total_score / valid_periods if valid_periods > 0 else 0.0
        382:     
    >>> 383:     def optimize(self, max_combinations: int = 500, backtest_periods: int = 15) -> Dict:
        384:         """执行参数优化（改进版）"""
        385:         print(f"🚀 开始贝叶斯参数优化")
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 480
  - **发现值**: 20
  - **上下文**:
    ```
        478: 
        479:         # 使用完整的参数设置进行优化
    >>> 480:         result = optimizer.optimize(max_combinations=1000, backtest_periods=20)
        481: 
        482:         if result and 'best_score' in result:
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 134
  - **发现值**: 20
  - **上下文**:
    ```
        132:             return False
        133: 
    >>> 134:     def evaluate_parameters(self, params: Dict[str, float], backtest_periods: int = 20) -> float:
        135:         """评估参数组合的性能（增强版）"""
        136:         try:
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 383
  - **发现值**: 15
  - **上下文**:
    ```
        381:         return total_score / valid_periods if valid_periods > 0 else 0.0
        382:     
    >>> 383:     def optimize(self, max_combinations: int = 500, backtest_periods: int = 15) -> Dict:
        384:         """执行参数优化（改进版）"""
        385:         print(f"🚀 开始贝叶斯参数优化")
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 480
  - **发现值**: 20
  - **上下文**:
    ```
        478: 
        479:         # 使用完整的参数设置进行优化
    >>> 480:         result = optimizer.optimize(max_combinations=1000, backtest_periods=20)
        481: 
        482:         if result and 'best_score' in result:
    ```

- **文件**: `optimize_kill_algorithm.py`
  - **行号**: 31
  - **发现值**: 30
  - **上下文**:
    ```
         29:     
         30:     # 分析最近30期的杀号失败模式
    >>>  31:     test_periods = 30
         32:     failure_analysis = {
         33:         'killed_but_appeared': [],  # 被杀但出现的号码
    ```

- **文件**: `optimize_predictor_parameters.py`
  - **行号**: 59
  - **发现值**: 100
  - **上下文**:
    ```
         57:         self.optimization_history = []
         58:         
    >>>  59:     def evaluate_parameters(self, params: Dict, test_periods: int = 100) -> Dict:
         60:         """
         61:         评估参数组合的性能
    ```

- **文件**: `optimize_predictor_parameters.py`
  - **行号**: 430
  - **发现值**: 200
  - **上下文**:
    ```
        428:         print("📊 分析当前参数性能...")
        429: 
    >>> 430:         current_result = self.evaluate_parameters(self.current_best_params, test_periods=200)
        431: 
        432:         print(f"当前参数性能分析:")
    ```

- **文件**: `optimize_predictor_parameters.py`
  - **行号**: 585
  - **发现值**: 80
  - **上下文**:
    ```
        583:             }
        584: 
    >>> 585:             result = self.evaluate_parameters(params, test_periods=80)
        586:             score = result['score']
        587: 
    ```

- **文件**: `test_6_algorithms.py`
  - **行号**: 34
  - **发现值**: 24010
  - **上下文**:
    ```
         32:     
         33:     # 测试多个期号
    >>>  34:     test_periods = ["24010", "24009", "24008", "24007", "24006"]
         35:     results = []
         36:     
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 29
  - **发现值**: 2
  - **上下文**:
    ```
         27:         # 测试回测功能
         28:         print("🧪 测试回测功能...")
    >>>  29:         result = predictor.run_backtest(num_periods=2, display_periods=2)
         30:         
         31:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 57
  - **发现值**: 2
  - **上下文**:
    ```
         55:         # 测试回测功能
         56:         print("🧪 测试回测功能...")
    >>>  57:         result = predictor.run_comprehensive_backtest(num_periods=2, display_periods=2)
         58:         
         59:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 111
  - **发现值**: 1
  - **上下文**:
    ```
        109:         
        110:         # 测试配置创建
    >>> 111:         config = BacktestConfig(num_periods=1, min_train_periods=10)
        112:         if config.validate():
        113:             print("✅ 配置验证成功")
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 195
  - **发现值**: 1
  - **上下文**:
    ```
        193:         # 通过运行一个小的回测来验证
        194:         print("🧪 运行小回测验证期号显示...")
    >>> 195:         result = predictor.run_backtest(num_periods=1, display_periods=1)
        196:         
        197:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 29
  - **发现值**: 2
  - **上下文**:
    ```
         27:         # 测试回测功能
         28:         print("🧪 测试回测功能...")
    >>>  29:         result = predictor.run_backtest(num_periods=2, display_periods=2)
         30:         
         31:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 195
  - **发现值**: 1
  - **上下文**:
    ```
        193:         # 通过运行一个小的回测来验证
        194:         print("🧪 运行小回测验证期号显示...")
    >>> 195:         result = predictor.run_backtest(num_periods=1, display_periods=1)
        196:         
        197:         if result:
    ```

- **文件**: `test_backtest_consistency.py`
  - **行号**: 73
  - **发现值**: 20
  - **上下文**:
    ```
         71:         
         72:         # 贝叶斯优化器使用的回测期数（默认20期，但限制为10期）
    >>>  73:         backtest_periods_param = 20
         74:         actual_periods = min(backtest_periods_param, 10)  # 限制回测期数
         75:         min_train_periods = 0  # 与主程序一致，使用所有可用的训练数据
    ```

- **文件**: `test_backtest_consistency.py`
  - **行号**: 73
  - **发现值**: 20
  - **上下文**:
    ```
         71:         
         72:         # 贝叶斯优化器使用的回测期数（默认20期，但限制为10期）
    >>>  73:         backtest_periods_param = 20
         74:         actual_periods = min(backtest_periods_param, 10)  # 限制回测期数
         75:         min_train_periods = 0  # 与主程序一致，使用所有可用的训练数据
    ```

- **文件**: `test_backtest_consistency.py`
  - **行号**: 73
  - **发现值**: 20
  - **上下文**:
    ```
         71:         
         72:         # 贝叶斯优化器使用的回测期数（默认20期，但限制为10期）
    >>>  73:         backtest_periods_param = 20
         74:         actual_periods = min(backtest_periods_param, 10)  # 限制回测期数
         75:         min_train_periods = 0  # 与主程序一致，使用所有可用的训练数据
    ```

- **文件**: `test_bayes_optimization.py`
  - **行号**: 158
  - **发现值**: 3
  - **上下文**:
    ```
        156:             
        157:             # 测试评估（使用很少的回测期数）
    >>> 158:             score = optimizer.evaluate_parameters(test_params, backtest_periods=3)
        159:             print(f"   评估得分: {score:.4f}")
        160:         
    ```

- **文件**: `test_bayes_optimization.py`
  - **行号**: 158
  - **发现值**: 3
  - **上下文**:
    ```
        156:             
        157:             # 测试评估（使用很少的回测期数）
    >>> 158:             score = optimizer.evaluate_parameters(test_params, backtest_periods=3)
        159:             print(f"   评估得分: {score:.4f}")
        160:         
    ```

- **文件**: `test_bayes_optimization.py`
  - **行号**: 158
  - **发现值**: 3
  - **上下文**:
    ```
        156:             
        157:             # 测试评估（使用很少的回测期数）
    >>> 158:             score = optimizer.evaluate_parameters(test_params, backtest_periods=3)
        159:             print(f"   评估得分: {score:.4f}")
        160:         
    ```

- **文件**: `test_corrected_backtest.py`
  - **行号**: 47
  - **发现值**: 5
  - **上下文**:
    ```
         45:     # 3. 配置回测参数
         46:     config = BacktestConfig(
    >>>  47:         num_periods=5,  # 只回测5期，快速验证
         48:         min_train_periods=100  # 最少100期训练数据
         49:     )
    ```

- **文件**: `test_corrected_backtest_order.py`
  - **行号**: 39
  - **发现值**: 5
  - **上下文**:
    ```
         37:     # 创建回测配置 - 只测试5期
         38:     config = BacktestConfig(
    >>>  39:         num_periods=5,
         40:         min_train_periods=50
         41:     )
    ```

- **文件**: `test_corrected_final_order.py`
  - **行号**: 23
  - **发现值**: 4
  - **上下文**:
    ```
         21:     
         22:     # 模拟回测配置
    >>>  23:     num_periods = 4
         24:     min_train_periods = 50
         25:     total_data_len = len(data)
    ```

- **文件**: `test_enhanced_kill_algorithm.py`
  - **行号**: 29
  - **发现值**: 20
  - **上下文**:
    ```
         27:     
         28:     # 测试最近20期的杀号效果
    >>>  29:     test_periods = 20
         30:     success_count = 0
         31:     total_periods = 0
    ```

- **文件**: `test_fixed_bayes_optimization.py`
  - **行号**: 39
  - **发现值**: 3
  - **上下文**:
    ```
         37:     try:
         38:         print(f"\n🔍 开始评估参数...")
    >>>  39:         score = optimizer.evaluate_parameters(test_params, backtest_periods=3)
         40:         print(f"✅ 评估完成")
         41:         print(f"综合得分: {score:.6f}")
    ```

- **文件**: `test_fixed_bayes_optimization.py`
  - **行号**: 68
  - **发现值**: 3
  - **上下文**:
    ```
         66:     try:
         67:         print(f"🚀 开始小规模优化...")
    >>>  68:         result = optimizer.optimize(max_combinations=5, backtest_periods=3)
         69:         
         70:         print(f"✅ 优化完成")
    ```

- **文件**: `test_fixed_bayes_optimization.py`
  - **行号**: 39
  - **发现值**: 3
  - **上下文**:
    ```
         37:     try:
         38:         print(f"\n🔍 开始评估参数...")
    >>>  39:         score = optimizer.evaluate_parameters(test_params, backtest_periods=3)
         40:         print(f"✅ 评估完成")
         41:         print(f"综合得分: {score:.6f}")
    ```

- **文件**: `test_fixed_bayes_optimization.py`
  - **行号**: 68
  - **发现值**: 3
  - **上下文**:
    ```
         66:     try:
         67:         print(f"🚀 开始小规模优化...")
    >>>  68:         result = optimizer.optimize(max_combinations=5, backtest_periods=3)
         69:         
         70:         print(f"✅ 优化完成")
    ```

- **文件**: `test_fixed_bayes_optimization.py`
  - **行号**: 39
  - **发现值**: 3
  - **上下文**:
    ```
         37:     try:
         38:         print(f"\n🔍 开始评估参数...")
    >>>  39:         score = optimizer.evaluate_parameters(test_params, backtest_periods=3)
         40:         print(f"✅ 评估完成")
         41:         print(f"综合得分: {score:.6f}")
    ```

- **文件**: `test_fixed_bayes_optimization.py`
  - **行号**: 68
  - **发现值**: 3
  - **上下文**:
    ```
         66:     try:
         67:         print(f"🚀 开始小规模优化...")
    >>>  68:         result = optimizer.optimize(max_combinations=5, backtest_periods=3)
         69:         
         70:         print(f"✅ 优化完成")
    ```

- **文件**: `test_time_series_logic.py`
  - **行号**: 50
  - **发现值**: 3
  - **上下文**:
    ```
         48:     # 配置小规模回测
         49:     config = BacktestConfig(
    >>>  50:         num_periods=3,  # 只测试3期
         51:         min_train_periods=10,  # 最少10期训练数据
         52:         display_periods=3,
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 45
  - **发现值**: 1
  - **上下文**:
    ```
         43:         
         44:         # 测试无效配置
    >>>  45:         invalid_config = BacktestConfig(num_periods=-1)
         46:         assert invalid_config.validate() == False
         47:         
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 148
  - **发现值**: 3
  - **上下文**:
    ```
        146:         # 创建配置
        147:         config = BacktestConfig(
    >>> 148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
        150:             display_periods=3
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `notebooks\hit_analysis.py`
  - **行号**: 22
  - **发现值**: 20
  - **上下文**:
    ```
         20:         self.predictor = LotteryPredictor()
         21:     
    >>>  22:     def analyze_prediction_vs_actual(self, num_periods: int = 20) -> None:
         23:         """分析预测号码与实际开奖的差异"""
         24:         print("=" * 60)
    ```

- **文件**: `tests\analyze_kill_failure_patterns.py`
  - **行号**: 27
  - **发现值**: 30
  - **上下文**:
    ```
         25:         self.results = []
         26:         
    >>>  27:     def analyze_patterns(self, test_periods=30):
         28:         """分析杀号失败模式"""
         29:         print("🔍 深入分析杀号失败模式")
    ```

- **文件**: `tests\analyze_kill_failure_patterns.py`
  - **行号**: 379
  - **发现值**: 30
  - **上下文**:
    ```
        377:     """主函数"""
        378:     analyzer = KillFailureAnalyzer()
    >>> 379:     analyzer.analyze_patterns(test_periods=30)
        380: 
        381: if __name__ == "__main__":
    ```

- **文件**: `tests\analyze_original_backtest_range.py`
  - **行号**: 111
  - **发现值**: 25068
  - **上下文**:
    ```
        109:     
        110:     # 对比我们的测试范围
    >>> 111:     our_test_periods = [25068, 25067, 25066, 25065, 25064, 25063, 25062, 25061, 25060]
        112:     print(f"\n📊 对比分析:")
        113:     print(f"   我们的测试期号: {our_test_periods}")
    ```

- **文件**: `tests\compare_test_methods.py`
  - **行号**: 29
  - **发现值**: 25068
  - **上下文**:
    ```
         27:     
         28:     # 测试相同的期号
    >>>  29:     test_periods = [25068, 25067, 25066, 25065, 25064, 25063, 25062, 25061, 25060]
         30:     
         31:     print("📊 使用200算法回测的方法测试Top 10算法:")
    ```

- **文件**: `tests\final_improved_kill_system.py`
  - **行号**: 287
  - **发现值**: 25068
  - **上下文**:
    ```
        285:     
        286:     # 测试几个期号
    >>> 287:     test_periods = ["25068", "25067", "25066"]
        288:     
        289:     for period in test_periods:
    ```

- **文件**: `tests\final_validation_test.py`
  - **行号**: 138
  - **发现值**: 25068
  - **上下文**:
    ```
        136:     
        137:     # 测试完整的预测接口
    >>> 138:     test_periods = [25068, 25067, 25066, 25065, 25064]
        139:     
        140:     for period in test_periods:
    ```

- **文件**: `tests\optimize_kill_algorithm.py`
  - **行号**: 31
  - **发现值**: 30
  - **上下文**:
    ```
         29:     
         30:     # 分析最近30期的杀号失败模式
    >>>  31:     test_periods = 30
         32:     failure_analysis = {
         33:         'killed_but_appeared': [],  # 被杀但出现的号码
    ```

- **文件**: `tests\test_6_algorithms.py`
  - **行号**: 34
  - **发现值**: 24010
  - **上下文**:
    ```
         32:     
         33:     # 测试多个期号
    >>>  34:     test_periods = ["24010", "24009", "24008", "24007", "24006"]
         35:     results = []
         36:     
    ```

- **文件**: `tests\test_enhanced_kill_algorithm.py`
  - **行号**: 32
  - **发现值**: 20
  - **上下文**:
    ```
         30:     
         31:     # 测试最近20期的杀号效果
    >>>  32:     test_periods = 20
         33:     success_count = 0
         34:     total_periods = 0
    ```

- **文件**: `tests\test_fixed_system.py`
  - **行号**: 133
  - **发现值**: 25068
  - **上下文**:
    ```
        131:         
        132:         # 测试几个期号的杀号预测
    >>> 133:         test_periods = ['25068', '25067', '25066']
        134:         
        135:         for period in test_periods:
    ```

- **文件**: `tests\test_fixed_top10.py`
  - **行号**: 28
  - **发现值**: 25068
  - **上下文**:
    ```
         26:     
         27:     # 测试相同的期号
    >>>  28:     test_periods = [25068, 25067, 25066, 25065, 25064, 25063, 25062, 25061, 25060]
         29:     
         30:     print("📊 使用修复后的Top 10系统测试:")
    ```

- **文件**: `tests\test_improved_vs_original.py`
  - **行号**: 18
  - **发现值**: 20
  - **上下文**:
    ```
         16: from tests.improved_kill_algorithm_v2 import ImprovedKillAlgorithmV2
         17: 
    >>>  18: def test_comparison(test_periods=20):
         19:     """对比测试原始算法和改进算法"""
         20:     print("🔬 对比测试：改进算法 vs 原始算法")
    ```

- **文件**: `tests\test_improved_vs_original.py`
  - **行号**: 200
  - **发现值**: 20
  - **上下文**:
    ```
        198: 
        199: if __name__ == "__main__":
    >>> 200:     test_comparison(test_periods=20)
        201: 
    ```

- **文件**: `tests\test_top10_backtest.py`
  - **行号**: 33
  - **发现值**: 0
  - **上下文**:
    ```
         31:         
         32:         # 选择测试期号（最近10期）
    >>>  33:         test_periods = data.iloc[0:10]['期号'].tolist()
         34:         print(f"📊 测试期号: {test_periods}")
         35:         
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 45
  - **发现值**: 1
  - **上下文**:
    ```
         43:         
         44:         # 测试无效配置
    >>>  45:         invalid_config = BacktestConfig(num_periods=-1)
         46:         assert invalid_config.validate() == False
         47:         
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 148
  - **发现值**: 3
  - **上下文**:
    ```
        146:         # 创建配置
        147:         config = BacktestConfig(
    >>> 148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
        150:             display_periods=3
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `src\core\base.py`
  - **行号**: 282
  - **发现值**: 50
  - **上下文**:
    ```
        280:     
        281:     @abstractmethod
    >>> 282:     def run_backtest(self, num_periods: int = 50, display_periods: int = 10) -> Dict[str, Any]:
        283:         """
        284:         运行回测
    ```

- **文件**: `src\features\external_features.py`
  - **行号**: 355
  - **发现值**: 25065
  - **上下文**:
    ```
        353:     
        354:     # 测试多个期号
    >>> 355:     test_periods = [25065, 25066, 25067, 25068]
        356:     features_matrix = extractor.extract_features_for_periods(test_periods)
        357:     
    ```

- **文件**: `src\framework\data_models.py`
  - **行号**: 27
  - **发现值**: 0
  - **上下文**:
    ```
         25:     def validate(self) -> bool:
         26:         """验证配置参数"""
    >>>  27:         if self.num_periods <= 0:
         28:             return False
         29:         if self.min_train_periods <= 0:
    ```

- **文件**: `src\systems\advanced_prediction_system.py`
  - **行号**: 353
  - **发现值**: 20
  - **上下文**:
    ```
        351:         return status
        352:     
    >>> 353:     def run_comprehensive_backtest(self, num_periods: int = 20) -> Dict:
        354:         """
        355:         运行综合回测
    ```

- **文件**: `src\systems\advanced_prediction_system.py`
  - **行号**: 475
  - **发现值**: 5
  - **上下文**:
    ```
        473:     # 运行小规模回测
        474:     try:
    >>> 475:         backtest_results = system.run_comprehensive_backtest(num_periods=5)
        476:         
        477:         print("\n📊 回测结果:")
    ```

- **文件**: `src\systems\basic_system.py`
  - **行号**: 467
  - **发现值**: 50
  - **上下文**:
    ```
        465:         }
        466:     
    >>> 467:     def run_backtest(self, num_periods: int = 50, display_periods: int = 10) -> None:
        468:         """
        469:         运行回测
    ```

- **文件**: `src\systems\basic_system.py`
  - **行号**: 797
  - **发现值**: 50
  - **上下文**:
    ```
        795:     try:
        796:         predictor = LotteryPredictor()
    >>> 797:         predictor.run_backtest(num_periods=50, display_periods=10)
        798:     except Exception as e:
        799:         print(f"程序运行出错: {e}")
    ```

- **文件**: `src\systems\basic_system.py`
  - **行号**: 396
  - **发现值**: 20
  - **上下文**:
    ```
        394:         try:
        395:             # 使用杀号器验证最近20期的成功率
    >>> 396:             test_periods = min(20, len(train_data) - 1)
        397:             if test_periods <= 0:
        398:                 return 0.95  # 默认高成功率
    ```

- **文件**: `src\systems\basic_system.py`
  - **行号**: 397
  - **发现值**: 0
  - **上下文**:
    ```
        395:             # 使用杀号器验证最近20期的成功率
        396:             test_periods = min(20, len(train_data) - 1)
    >>> 397:             if test_periods <= 0:
        398:                 return 0.95  # 默认高成功率
        399: 
    ```

- **文件**: `src\systems\basic_system.py`
  - **行号**: 797
  - **发现值**: 50
  - **上下文**:
    ```
        795:     try:
        796:         predictor = LotteryPredictor()
    >>> 797:         predictor.run_backtest(num_periods=50, display_periods=10)
        798:     except Exception as e:
        799:         print(f"程序运行出错: {e}")
    ```

- **文件**: `src\systems\main.py`
  - **行号**: 1405
  - **发现值**: 50
  - **上下文**:
    ```
        1403:             return self.run_backtest_legacy(num_periods, display_periods)
        1404: 
    >>> 1405:     def run_backtest_legacy(self, num_periods: int = 50, display_periods: int = 10) -> None:
        1406:         """
        1407:         原始回测方法（备份）
    ```

- **文件**: `src\systems\main.py`
  - **行号**: 2251
  - **发现值**: 40
  - **上下文**:
    ```
        2249:     try:
        2250:         predictor = LotteryPredictor()
    >>> 2251:         predictor.run_backtest(num_periods=40, display_periods=40)
        2252:     except Exception as e:
        2253:         print(f"程序运行出错: {e}")
    ```

- **文件**: `src\systems\main.py`
  - **行号**: 2251
  - **发现值**: 40
  - **上下文**:
    ```
        2249:     try:
        2250:         predictor = LotteryPredictor()
    >>> 2251:         predictor.run_backtest(num_periods=40, display_periods=40)
        2252:     except Exception as e:
        2253:         print(f"程序运行出错: {e}")
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 221
  - **发现值**: 30
  - **上下文**:
    ```
        219:             return prediction
        220:     
    >>> 221:     def run_comprehensive_backtest(self, num_periods: int = 30, display_periods: int = 5) -> Dict:
        222:         """
        223:         运行综合回测（比较所有可用模式）- 使用统一框架
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 297
  - **发现值**: 30
  - **上下文**:
    ```
        295:             return self._run_comprehensive_backtest_legacy(num_periods, display_periods)
        296:     
    >>> 297:     def _run_comprehensive_backtest_legacy(self, num_periods: int = 30, display_periods: int = 5) -> Dict:
        298:         """原始综合回测方法（备份）"""
        299:         print("🧪 开始综合回测（原始方法）...")
    ```

- **文件**: `src\systems\super_prediction_system.py`
  - **行号**: 461
  - **发现值**: 15
  - **上下文**:
    ```
        459:         return status
        460:     
    >>> 461:     def run_super_backtest(self, num_periods: int = 15) -> Dict:
        462:         """
        463:         运行超级回测
    ```

- **文件**: `src\utils\universal_killer.py`
  - **行号**: 1265
  - **发现值**: 100
  - **上下文**:
    ```
        1263:         return [num for num, score in ranked]
        1264:     
    >>> 1265:     def validate_accuracy(self, data: pd.DataFrame, test_periods: int = 100) -> Dict:
        1266:         """验证通杀公式的准确率"""
        1267:         from src.utils.utils import parse_numbers
    ```

- **文件**: `final_consistency_report.md`
  - **行号**: 44
  - **发现值**: 50
  - **上下文**:
    ```
         42: ```python
         43: # 修改前
    >>>  44: def evaluate_parameters(self, params: Dict, test_periods: int = 50) -> Dict:
         45: 
         46: # 修改后  
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 38
  - **发现值**: 50
  - **上下文**:
    ```
         36: **原始代码（main.py）：**
         37: ```python
    >>>  38: def run_backtest(self, num_periods: int = 50, display_periods: int = 10) -> None:
         39:     # 直接在main中实现回测逻辑
         40:     for i in range(max_backtest):
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 60
  - **发现值**: 50
  - **上下文**:
    ```
         58: 
         59: # 2. 使用新框架
    >>>  60: def run_backtest_new(self, num_periods: int = 50, display_periods: int = 10) -> None:
         61:     from src.framework import BacktestFramework, BacktestConfig, ResultDisplayer
         62:     
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 88
  - **发现值**: 50
  - **上下文**:
    ```
         86: **原始的run_backtest方法：**
         87: ```python
    >>>  88: def run_backtest(self, num_periods: int = 50, display_periods: int = 10) -> None:
         89:     """运行回测"""
         90:     print(f"开始回测最近 {num_periods} 期...")
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 98
  - **发现值**: 50
  - **上下文**:
    ```
         96: **迁移后：**
         97: ```python
    >>>  98: def run_backtest(self, num_periods: int = 50, display_periods: int = 10) -> None:
         99:     """运行回测 - 使用统一框架"""
        100:     from src.framework import BacktestFramework, BacktestConfig, ResultDisplayer
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 134
  - **发现值**: 30
  - **上下文**:
    ```
        132: **原始代码：**
        133: ```python
    >>> 134: def run_comprehensive_backtest(self, num_periods: int = 30):
        135:     # 多种模式的回测逻辑
        136:     for mode in ['bayes', 'markov', 'combined']:
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 142
  - **发现值**: 30
  - **上下文**:
    ```
        140: **迁移后：**
        141: ```python
    >>> 142: def run_comprehensive_backtest(self, num_periods: int = 30):
        143:     """综合回测 - 使用统一框架"""
        144:     from src.framework import BacktestFramework, BacktestConfig
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 205
  - **发现值**: 30
  - **上下文**:
    ```
        203:     # 配置回测（专注于杀号测试）
        204:     config = BacktestConfig(
    >>> 205:         num_periods=30,
        206:         min_train_periods=20,
        207:         display_periods=10,
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 244
  - **发现值**: 5
  - **上下文**:
    ```
        242:     # 运行新框架回测
        243:     print("🧪 测试新框架回测...")
    >>> 244:     result = predictor.run_backtest(num_periods=5, display_periods=3)
        245:     
        246:     # 验证结果
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 287
  - **发现值**: 50
  - **上下文**:
    ```
        285: 1. **保留原始方法**：
        286: ```python
    >>> 287: def run_backtest_old(self, num_periods: int = 50, display_periods: int = 10):
        288:     """原始回测方法（备份）"""
        289:     # 保留原始实现
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 292
  - **发现值**: 50
  - **上下文**:
    ```
        290:     pass
        291: 
    >>> 292: def run_backtest(self, num_periods: int = 50, display_periods: int = 10):
        293:     """新回测方法"""
        294:     try:
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 244
  - **发现值**: 5
  - **上下文**:
    ```
        242:     # 运行新框架回测
        243:     print("🧪 测试新框架回测...")
    >>> 244:     result = predictor.run_backtest(num_periods=5, display_periods=3)
        245:     
        246:     # 验证结果
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 263
  - **发现值**: 3
  - **上下文**:
    ```
        261: 
        262: # 减少回测期数进行快速测试
    >>> 263: config.num_periods = 3
        264: 
        265: # 检查单期预测
    ```

### display_periods
**期望值**: 10

- **文件**: `check_framework_integrity.py`
  - **行号**: 241
  - **发现值**: 1
  - **上下文**:
    ```
        239:             num_periods=1,
        240:             min_train_periods=10,
    >>> 241:             display_periods=1
        242:         )
        243:         
    ```

- **文件**: `check_framework_integrity.py`
  - **行号**: 241
  - **发现值**: 1
  - **上下文**:
    ```
        239:             num_periods=1,
        240:             min_train_periods=10,
    >>> 241:             display_periods=1
        242:         )
        243:         
    ```

- **文件**: `check_framework_integrity.py`
  - **行号**: 241
  - **发现值**: 1
  - **上下文**:
    ```
        239:             num_periods=1,
        240:             min_train_periods=10,
    >>> 241:             display_periods=1
        242:         )
        243:         
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 159
  - **发现值**: 3
  - **上下文**:
    ```
        157:                 num_periods=min(backtest_periods, 10),  # 限制回测期数
        158:                 min_train_periods=0,  # 与主程序一致，使用所有可用的训练数据
    >>> 159:                 display_periods=3,
        160:                 enable_detailed_output=False,
        161:                 enable_statistics=True,
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 159
  - **发现值**: 3
  - **上下文**:
    ```
        157:                 num_periods=min(backtest_periods, 10),  # 限制回测期数
        158:                 min_train_periods=0,  # 与主程序一致，使用所有可用的训练数据
    >>> 159:                 display_periods=3,
        160:                 enable_detailed_output=False,
        161:                 enable_statistics=True,
    ```

- **文件**: `optimize_bayes_parameters.py`
  - **行号**: 159
  - **发现值**: 3
  - **上下文**:
    ```
        157:                 num_periods=min(backtest_periods, 10),  # 限制回测期数
        158:                 min_train_periods=0,  # 与主程序一致，使用所有可用的训练数据
    >>> 159:                 display_periods=3,
        160:                 enable_detailed_output=False,
        161:                 enable_statistics=True,
    ```

- **文件**: `optimize_predictor_parameters_unified.py`
  - **行号**: 68
  - **发现值**: 0
  - **上下文**:
    ```
         66:                 num_periods=test_periods,
         67:                 min_train_periods=0,   # 与主程序一致，使用所有可用的训练数据
    >>>  68:                 display_periods=0,     # 不显示详细结果
         69:                 enable_detailed_output=False,
         70:                 enable_statistics=True
    ```

- **文件**: `optimize_predictor_parameters_unified.py`
  - **行号**: 68
  - **发现值**: 0
  - **上下文**:
    ```
         66:                 num_periods=test_periods,
         67:                 min_train_periods=0,   # 与主程序一致，使用所有可用的训练数据
    >>>  68:                 display_periods=0,     # 不显示详细结果
         69:                 enable_detailed_output=False,
         70:                 enable_statistics=True
    ```

- **文件**: `optimize_predictor_parameters_unified.py`
  - **行号**: 68
  - **发现值**: 0
  - **上下文**:
    ```
         66:                 num_periods=test_periods,
         67:                 min_train_periods=0,   # 与主程序一致，使用所有可用的训练数据
    >>>  68:                 display_periods=0,     # 不显示详细结果
         69:                 enable_detailed_output=False,
         70:                 enable_statistics=True
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 29
  - **发现值**: 2
  - **上下文**:
    ```
         27:         # 测试回测功能
         28:         print("🧪 测试回测功能...")
    >>>  29:         result = predictor.run_backtest(num_periods=2, display_periods=2)
         30:         
         31:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 57
  - **发现值**: 2
  - **上下文**:
    ```
         55:         # 测试回测功能
         56:         print("🧪 测试回测功能...")
    >>>  57:         result = predictor.run_comprehensive_backtest(num_periods=2, display_periods=2)
         58:         
         59:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 195
  - **发现值**: 1
  - **上下文**:
    ```
        193:         # 通过运行一个小的回测来验证
        194:         print("🧪 运行小回测验证期号显示...")
    >>> 195:         result = predictor.run_backtest(num_periods=1, display_periods=1)
        196:         
        197:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 29
  - **发现值**: 2
  - **上下文**:
    ```
         27:         # 测试回测功能
         28:         print("🧪 测试回测功能...")
    >>>  29:         result = predictor.run_backtest(num_periods=2, display_periods=2)
         30:         
         31:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 57
  - **发现值**: 2
  - **上下文**:
    ```
         55:         # 测试回测功能
         56:         print("🧪 测试回测功能...")
    >>>  57:         result = predictor.run_comprehensive_backtest(num_periods=2, display_periods=2)
         58:         
         59:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 195
  - **发现值**: 1
  - **上下文**:
    ```
        193:         # 通过运行一个小的回测来验证
        194:         print("🧪 运行小回测验证期号显示...")
    >>> 195:         result = predictor.run_backtest(num_periods=1, display_periods=1)
        196:         
        197:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 29
  - **发现值**: 2
  - **上下文**:
    ```
         27:         # 测试回测功能
         28:         print("🧪 测试回测功能...")
    >>>  29:         result = predictor.run_backtest(num_periods=2, display_periods=2)
         30:         
         31:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 57
  - **发现值**: 2
  - **上下文**:
    ```
         55:         # 测试回测功能
         56:         print("🧪 测试回测功能...")
    >>>  57:         result = predictor.run_comprehensive_backtest(num_periods=2, display_periods=2)
         58:         
         59:         if result:
    ```

- **文件**: `test_all_systems.py`
  - **行号**: 195
  - **发现值**: 1
  - **上下文**:
    ```
        193:         # 通过运行一个小的回测来验证
        194:         print("🧪 运行小回测验证期号显示...")
    >>> 195:         result = predictor.run_backtest(num_periods=1, display_periods=1)
        196:         
        197:         if result:
    ```

- **文件**: `test_time_series_logic.py`
  - **行号**: 52
  - **发现值**: 3
  - **上下文**:
    ```
         50:         num_periods=3,  # 只测试3期
         51:         min_train_periods=10,  # 最少10期训练数据
    >>>  52:         display_periods=3,
         53:         enable_detailed_output=True,
         54:         enable_statistics=True,
    ```

- **文件**: `test_time_series_logic.py`
  - **行号**: 52
  - **发现值**: 3
  - **上下文**:
    ```
         50:         num_periods=3,  # 只测试3期
         51:         min_train_periods=10,  # 最少10期训练数据
    >>>  52:         display_periods=3,
         53:         enable_detailed_output=True,
         54:         enable_statistics=True,
    ```

- **文件**: `test_time_series_logic.py`
  - **行号**: 52
  - **发现值**: 3
  - **上下文**:
    ```
         50:         num_periods=3,  # 只测试3期
         51:         min_train_periods=10,  # 最少10期训练数据
    >>>  52:         display_periods=3,
         53:         enable_detailed_output=True,
         54:         enable_statistics=True,
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 150
  - **发现值**: 3
  - **上下文**:
    ```
        148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
    >>> 150:             display_periods=3
        151:         )
        152:         
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 150
  - **发现值**: 3
  - **上下文**:
    ```
        148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
    >>> 150:             display_periods=3
        151:         )
        152:         
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 150
  - **发现值**: 3
  - **上下文**:
    ```
        148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
    >>> 150:             display_periods=3
        151:         )
        152:         
    ```

- **文件**: `test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 150
  - **发现值**: 3
  - **上下文**:
    ```
        148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
    >>> 150:             display_periods=3
        151:         )
        152:         
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 150
  - **发现值**: 3
  - **上下文**:
    ```
        148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
    >>> 150:             display_periods=3
        151:         )
        152:         
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 150
  - **发现值**: 3
  - **上下文**:
    ```
        148:             num_periods=3,      # 只测试3期
        149:             min_train_periods=5, # 最少5期训练
    >>> 150:             display_periods=3
        151:         )
        152:         
    ```

- **文件**: `tests\test_unified_framework.py`
  - **行号**: 182
  - **发现值**: 3
  - **上下文**:
    ```
        180:         
        181:         # 创建模拟结果
    >>> 182:         config = BacktestConfig(num_periods=3, display_periods=3)
        183:         stats = Statistics(
        184:             total_periods=3,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 45
  - **发现值**: 5
  - **上下文**:
    ```
         43:             num_periods=10,           # 回测10期
         44:             min_train_periods=50,     # 最少50期训练数据
    >>>  45:             display_periods=5,        # 显示最近5期
         46:             enable_detailed_output=True,
         47:             enable_statistics=True,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 103
  - **发现值**: 5
  - **上下文**:
    ```
        101:             num_periods=10,
        102:             min_train_periods=30,
    >>> 103:             display_periods=5,
        104:             metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号指标
        105:             enable_detailed_output=True,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 45
  - **发现值**: 5
  - **上下文**:
    ```
         43:             num_periods=10,           # 回测10期
         44:             min_train_periods=50,     # 最少50期训练数据
    >>>  45:             display_periods=5,        # 显示最近5期
         46:             enable_detailed_output=True,
         47:             enable_statistics=True,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 103
  - **发现值**: 5
  - **上下文**:
    ```
        101:             num_periods=10,
        102:             min_train_periods=30,
    >>> 103:             display_periods=5,
        104:             metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号指标
        105:             enable_detailed_output=True,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 45
  - **发现值**: 5
  - **上下文**:
    ```
         43:             num_periods=10,           # 回测10期
         44:             min_train_periods=50,     # 最少50期训练数据
    >>>  45:             display_periods=5,        # 显示最近5期
         46:             enable_detailed_output=True,
         47:             enable_statistics=True,
    ```

- **文件**: `src\framework\example_usage.py`
  - **行号**: 103
  - **发现值**: 5
  - **上下文**:
    ```
        101:             num_periods=10,
        102:             min_train_periods=30,
    >>> 103:             display_periods=5,
        104:             metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号指标
        105:             enable_detailed_output=True,
    ```

- **文件**: `src\framework\predictor_adapter.py`
  - **行号**: 403
  - **发现值**: 5
  - **上下文**:
    ```
        401:         num_periods=10,
        402:         min_train_periods=50,
    >>> 403:         display_periods=5
        404:     )
        405:     
    ```

- **文件**: `src\framework\predictor_adapter.py`
  - **行号**: 403
  - **发现值**: 5
  - **上下文**:
    ```
        401:         num_periods=10,
        402:         min_train_periods=50,
    >>> 403:         display_periods=5
        404:     )
        405:     
    ```

- **文件**: `src\framework\predictor_adapter.py`
  - **行号**: 403
  - **发现值**: 5
  - **上下文**:
    ```
        401:         num_periods=10,
        402:         min_train_periods=50,
    >>> 403:         display_periods=5
        404:     )
        405:     
    ```

- **文件**: `src\systems\main.py`
  - **行号**: 2251
  - **发现值**: 40
  - **上下文**:
    ```
        2249:     try:
        2250:         predictor = LotteryPredictor()
    >>> 2251:         predictor.run_backtest(num_periods=40, display_periods=40)
        2252:     except Exception as e:
        2253:         print(f"程序运行出错: {e}")
    ```

- **文件**: `src\systems\main.py`
  - **行号**: 2251
  - **发现值**: 40
  - **上下文**:
    ```
        2249:     try:
        2250:         predictor = LotteryPredictor()
    >>> 2251:         predictor.run_backtest(num_periods=40, display_periods=40)
        2252:     except Exception as e:
        2253:         print(f"程序运行出错: {e}")
    ```

- **文件**: `src\systems\main.py`
  - **行号**: 2251
  - **发现值**: 40
  - **上下文**:
    ```
        2249:     try:
        2250:         predictor = LotteryPredictor()
    >>> 2251:         predictor.run_backtest(num_periods=40, display_periods=40)
        2252:     except Exception as e:
        2253:         print(f"程序运行出错: {e}")
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 221
  - **发现值**: 5
  - **上下文**:
    ```
        219:             return prediction
        220:     
    >>> 221:     def run_comprehensive_backtest(self, num_periods: int = 30, display_periods: int = 5) -> Dict:
        222:         """
        223:         运行综合回测（比较所有可用模式）- 使用统一框架
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 297
  - **发现值**: 5
  - **上下文**:
    ```
        295:             return self._run_comprehensive_backtest_legacy(num_periods, display_periods)
        296:     
    >>> 297:     def _run_comprehensive_backtest_legacy(self, num_periods: int = 30, display_periods: int = 5) -> Dict:
        298:         """原始综合回测方法（备份）"""
        299:         print("🧪 开始综合回测（原始方法）...")
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 221
  - **发现值**: 5
  - **上下文**:
    ```
        219:             return prediction
        220:     
    >>> 221:     def run_comprehensive_backtest(self, num_periods: int = 30, display_periods: int = 5) -> Dict:
        222:         """
        223:         运行综合回测（比较所有可用模式）- 使用统一框架
    ```

- **文件**: `src\systems\main_ultimate.py`
  - **行号**: 297
  - **发现值**: 5
  - **上下文**:
    ```
        295:             return self._run_comprehensive_backtest_legacy(num_periods, display_periods)
        296:     
    >>> 297:     def _run_comprehensive_backtest_legacy(self, num_periods: int = 30, display_periods: int = 5) -> Dict:
        298:         """原始综合回测方法（备份）"""
        299:         print("🧪 开始综合回测（原始方法）...")
    ```

- **文件**: `tests\integration\test_final.py`
  - **行号**: 12
  - **发现值**: 5
  - **上下文**:
    ```
         10:         predictor = LotteryPredictor()
         11:         # 只回测10期，显示5期结果
    >>>  12:         predictor.run_backtest(num_periods=10, display_periods=5)
         13:     except Exception as e:
         14:         print(f"程序运行出错: {e}")
    ```

- **文件**: `tests\integration\test_final.py`
  - **行号**: 12
  - **发现值**: 5
  - **上下文**:
    ```
         10:         predictor = LotteryPredictor()
         11:         # 只回测10期，显示5期结果
    >>>  12:         predictor.run_backtest(num_periods=10, display_periods=5)
         13:     except Exception as e:
         14:         print(f"程序运行出错: {e}")
    ```

- **文件**: `tests\integration\test_final.py`
  - **行号**: 12
  - **发现值**: 5
  - **上下文**:
    ```
         10:         predictor = LotteryPredictor()
         11:         # 只回测10期，显示5期结果
    >>>  12:         predictor.run_backtest(num_periods=10, display_periods=5)
         13:     except Exception as e:
         14:         print(f"程序运行出错: {e}")
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 165
  - **发现值**: 5
  - **上下文**:
    ```
        163:             num_periods=num_periods,
        164:             min_train_periods=30,
    >>> 165:             display_periods=5
        166:         )
        167:         
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 244
  - **发现值**: 3
  - **上下文**:
    ```
        242:     # 运行新框架回测
        243:     print("🧪 测试新框架回测...")
    >>> 244:     result = predictor.run_backtest(num_periods=5, display_periods=3)
        245:     
        246:     # 验证结果
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 165
  - **发现值**: 5
  - **上下文**:
    ```
        163:             num_periods=num_periods,
        164:             min_train_periods=30,
    >>> 165:             display_periods=5
        166:         )
        167:         
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 244
  - **发现值**: 3
  - **上下文**:
    ```
        242:     # 运行新框架回测
        243:     print("🧪 测试新框架回测...")
    >>> 244:     result = predictor.run_backtest(num_periods=5, display_periods=3)
        245:     
        246:     # 验证结果
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 165
  - **发现值**: 5
  - **上下文**:
    ```
        163:             num_periods=num_periods,
        164:             min_train_periods=30,
    >>> 165:             display_periods=5
        166:         )
        167:         
    ```

- **文件**: `docs\migration_guide.md`
  - **行号**: 244
  - **发现值**: 3
  - **上下文**:
    ```
        242:     # 运行新框架回测
        243:     print("🧪 测试新框架回测...")
    >>> 244:     result = predictor.run_backtest(num_periods=5, display_periods=3)
        245:     
        246:     # 验证结果
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 73
  - **发现值**: 5
  - **上下文**:
    ```
         71:     num_periods=10,           # 回测期数
         72:     min_train_periods=50,     # 最少训练期数
    >>>  73:     display_periods=5         # 显示期数
         74: )
         75: 
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 91
  - **发现值**: 5
  - **上下文**:
    ```
         89:     num_periods=10,                    # 回测期数
         90:     min_train_periods=50,              # 最少训练期数
    >>>  91:     display_periods=5,                 # 显示期数
         92:     metrics=[                          # 评估指标
         93:         'red_odd_even_hit', 'red_size_hit', 'blue_size_hit',
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 73
  - **发现值**: 5
  - **上下文**:
    ```
         71:     num_periods=10,           # 回测期数
         72:     min_train_periods=50,     # 最少训练期数
    >>>  73:     display_periods=5         # 显示期数
         74: )
         75: 
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 91
  - **发现值**: 5
  - **上下文**:
    ```
         89:     num_periods=10,                    # 回测期数
         90:     min_train_periods=50,              # 最少训练期数
    >>>  91:     display_periods=5,                 # 显示期数
         92:     metrics=[                          # 评估指标
         93:         'red_odd_even_hit', 'red_size_hit', 'blue_size_hit',
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 73
  - **发现值**: 5
  - **上下文**:
    ```
         71:     num_periods=10,           # 回测期数
         72:     min_train_periods=50,     # 最少训练期数
    >>>  73:     display_periods=5         # 显示期数
         74: )
         75: 
    ```

- **文件**: `docs\unified_backtest_framework.md`
  - **行号**: 91
  - **发现值**: 5
  - **上下文**:
    ```
         89:     num_periods=10,                    # 回测期数
         90:     min_train_periods=50,              # 最少训练期数
    >>>  91:     display_periods=5,                 # 显示期数
         92:     metrics=[                          # 评估指标
         93:         'red_odd_even_hit', 'red_size_hit', 'blue_size_hit',
    ```

## 🚨 关键问题
- **min_train_periods**: 在 46 个文件中发现不一致的值
  - 涉及文件: advanced_probabilistic_system.py, advanced_probabilistic_system.py, check_framework_integrity.py, check_framework_integrity.py, test_all_systems.py, test_all_systems.py, test_corrected_backtest.py, test_corrected_backtest.py, test_corrected_backtest_order.py, test_corrected_backtest_order.py, test_corrected_final_order.py, test_time_series_logic.py, test_time_series_logic.py, test_unified_framework.py, test_unified_framework.py, test_unified_framework.py, test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\predictor_adapter.py, src\framework\predictor_adapter.py, src\systems\main.py, src\systems\main_ultimate.py, src\systems\main_ultimate.py, final_consistency_report.md, final_consistency_report.md, final_consistency_report.md, final_consistency_report.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md
- **backtest_periods**: 在 100 个文件中发现不一致的值
  - 涉及文件: backtest_6_algorithms.py, backtest_6_algorithms.py, backtest_6_algorithms.py, blue_ball_analysis_system.py, check_framework_integrity.py, debug_actual_backtest.py, debug_bayes_selection.py, debug_bayes_selection.py, debug_bayes_selection.py, debug_blue_ball_prediction.py, demo_parameter_optimization.py, fixed_parameter_optimization.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_kill_algorithm.py, optimize_predictor_parameters.py, optimize_predictor_parameters.py, optimize_predictor_parameters.py, test_6_algorithms.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_backtest_consistency.py, test_backtest_consistency.py, test_backtest_consistency.py, test_bayes_optimization.py, test_bayes_optimization.py, test_bayes_optimization.py, test_corrected_backtest.py, test_corrected_backtest_order.py, test_corrected_final_order.py, test_enhanced_kill_algorithm.py, test_fixed_bayes_optimization.py, test_fixed_bayes_optimization.py, test_fixed_bayes_optimization.py, test_fixed_bayes_optimization.py, test_fixed_bayes_optimization.py, test_fixed_bayes_optimization.py, test_time_series_logic.py, test_unified_framework.py, test_unified_framework.py, test_unified_framework.py, notebooks\hit_analysis.py, tests\analyze_kill_failure_patterns.py, tests\analyze_kill_failure_patterns.py, tests\analyze_original_backtest_range.py, tests\compare_test_methods.py, tests\final_improved_kill_system.py, tests\final_validation_test.py, tests\optimize_kill_algorithm.py, tests\test_6_algorithms.py, tests\test_enhanced_kill_algorithm.py, tests\test_fixed_system.py, tests\test_fixed_top10.py, tests\test_improved_vs_original.py, tests\test_improved_vs_original.py, tests\test_top10_backtest.py, tests\test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, src\core\base.py, src\features\external_features.py, src\framework\data_models.py, src\systems\advanced_prediction_system.py, src\systems\advanced_prediction_system.py, src\systems\basic_system.py, src\systems\basic_system.py, src\systems\basic_system.py, src\systems\basic_system.py, src\systems\basic_system.py, src\systems\main.py, src\systems\main.py, src\systems\main.py, src\systems\main_ultimate.py, src\systems\main_ultimate.py, src\systems\super_prediction_system.py, src\utils\universal_killer.py, final_consistency_report.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\unified_backtest_framework.md
- **display_periods**: 在 64 个文件中发现不一致的值
  - 涉及文件: check_framework_integrity.py, check_framework_integrity.py, check_framework_integrity.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_bayes_parameters.py, optimize_predictor_parameters_unified.py, optimize_predictor_parameters_unified.py, optimize_predictor_parameters_unified.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_all_systems.py, test_time_series_logic.py, test_time_series_logic.py, test_time_series_logic.py, test_unified_framework.py, test_unified_framework.py, test_unified_framework.py, test_unified_framework.py, test_unified_framework.py, test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, tests\test_unified_framework.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\example_usage.py, src\framework\predictor_adapter.py, src\framework\predictor_adapter.py, src\framework\predictor_adapter.py, src\systems\main.py, src\systems\main.py, src\systems\main.py, src\systems\main_ultimate.py, src\systems\main_ultimate.py, src\systems\main_ultimate.py, src\systems\main_ultimate.py, tests\integration\test_final.py, tests\integration\test_final.py, tests\integration\test_final.py, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\migration_guide.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md, docs\unified_backtest_framework.md

## 💡 修复建议
- 统一所有配置文件中的参数值
- 建立单一配置源，其他地方引用该源
- 添加配置验证机制
- 建立配置修改检查清单
