#!/usr/bin/env python3
"""
彩票预测系统 - 主入口点

这是重构后的主入口点，调用 src/apps/main.py 中的实际实现。
保持向后兼容性，用户仍然可以通过根目录的脚本启动系统。
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from apps.main import main
    
    if __name__ == "__main__":
        print("🎯 彩票预测系统启动中...")
        print("📁 使用重构后的项目结构")
        print("=" * 50)
        main()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保项目结构正确，src/apps/main.py 文件存在")
    sys.exit(1)
except Exception as e:
    print(f"❌ 运行错误: {e}")
    sys.exit(1)
