#!/usr/bin/env python3
"""
参数优化演示脚本
展示如何优化预测器参数以提升性能
"""

import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.utils.utils import load_data, parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, calculate_size_ratio_blue, ratio_to_state
    from src.models.improved_predictor import ImprovedPredictor
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)


def evaluate_parameter_set(params: Dict, test_periods: int = 50) -> Dict:
    """评估参数组合的性能"""
    print(f"📊 评估参数组合...")
    
    # 加载数据
    data = load_data()
    
    # 创建预测器并应用参数
    predictor = ImprovedPredictor()
    predictor.strategy_weights = params['strategy_weights']
    predictor.feature_predictability = params['feature_predictability']
    
    # 选择测试期数
    start_idx = max(0, len(data) - test_periods - 20)
    end_idx = len(data) - 1
    
    hits = {'red_odd_even': 0, 'red_size': 0, 'blue_size': 0, 'total_2_1': 0}
    total_tests = 0
    
    for i in range(start_idx, end_idx):
        try:
            # 获取当前期和下一期数据
            current_row = data.iloc[i]
            next_row = data.iloc[i + 1]
            
            # 解析号码
            next_red, next_blue = parse_numbers(next_row)
            
            # 计算实际状态
            next_red_odd, next_red_even = calculate_odd_even_ratio(next_red)
            actual_red_odd_even = ratio_to_state((next_red_odd, next_red_even))
            
            next_red_small, next_red_big = calculate_size_ratio_red(next_red)
            actual_red_size = ratio_to_state((next_red_small, next_red_big))
            
            next_blue_small, next_blue_big = calculate_size_ratio_blue(next_blue)
            actual_blue_size = ratio_to_state((next_blue_small, next_blue_big))
            
            # 进行预测
            prediction_result = predictor.predict_with_insights(i)
            predictions = prediction_result['predictions']
            
            # 检查命中情况
            red_odd_even_pred = predictions['red_odd_even'][0][0]
            red_size_pred = predictions['red_size'][0][0]
            blue_size_pred = predictions['blue_size'][0][0]
            
            if red_odd_even_pred == actual_red_odd_even:
                hits['red_odd_even'] += 1
            
            if red_size_pred == actual_red_size:
                hits['red_size'] += 1
            
            if blue_size_pred == actual_blue_size:
                hits['blue_size'] += 1
            
            # 2+1命中率
            if (red_odd_even_pred == actual_red_odd_even and 
                red_size_pred == actual_red_size and 
                blue_size_pred == actual_blue_size):
                hits['total_2_1'] += 1
            
            total_tests += 1
            
        except Exception as e:
            continue
    
    if total_tests == 0:
        return {'score': 0.0, 'details': {}}
    
    # 计算命中率
    rates = {
        'red_odd_even_rate': hits['red_odd_even'] / total_tests,
        'red_size_rate': hits['red_size'] / total_tests,
        'blue_size_rate': hits['blue_size'] / total_tests,
        'total_2_1_rate': hits['total_2_1'] / total_tests
    }
    
    # 综合评分
    score = (
        rates['red_odd_even_rate'] * 0.25 +
        rates['red_size_rate'] * 0.25 +
        rates['blue_size_rate'] * 0.30 +
        rates['total_2_1_rate'] * 0.20
    )
    
    return {
        'score': score,
        'details': {**rates, 'total_tests': total_tests, **hits}
    }


def demo_quick_optimization():
    """演示快速参数优化"""
    print("🎯 预测器参数优化演示")
    print("=" * 60)
    
    # 当前参数（基线）
    current_params = {
        'strategy_weights': {
            'trend_following': 0.40,
            'mean_reversion': 0.30,
            'persistence': 0.20,
            'frequency_based': 0.10
        },
        'feature_predictability': {
            'red_odd_even': 0.60,
            'red_size': 0.55,
            'blue_size': 0.473
        }
    }
    
    print("\n1️⃣ 评估当前参数性能")
    current_result = evaluate_parameter_set(current_params)
    print(f"当前参数性能:")
    print(f"  综合得分: {current_result['score']:.4f}")
    print(f"  红球奇偶命中率: {current_result['details']['red_odd_even_rate']:.3f}")
    print(f"  红球大小命中率: {current_result['details']['red_size_rate']:.3f}")
    print(f"  蓝球大小命中率: {current_result['details']['blue_size_rate']:.3f}")
    print(f"  2+1命中率: {current_result['details']['total_2_1_rate']:.3f}")
    
    # 优化策略
    optimization_strategies = [
        {
            'name': '增强蓝球预测',
            'strategy_weights': {
                'trend_following': 0.35,
                'mean_reversion': 0.35,
                'persistence': 0.20,
                'frequency_based': 0.10
            },
            'feature_predictability': {
                'red_odd_even': 0.58,
                'red_size': 0.52,
                'blue_size': 0.55
            }
        },
        {
            'name': '强化趋势跟随',
            'strategy_weights': {
                'trend_following': 0.45,
                'mean_reversion': 0.25,
                'persistence': 0.20,
                'frequency_based': 0.10
            },
            'feature_predictability': {
                'red_odd_even': 0.65,
                'red_size': 0.60,
                'blue_size': 0.48
            }
        },
        {
            'name': '平衡优化',
            'strategy_weights': {
                'trend_following': 0.38,
                'mean_reversion': 0.32,
                'persistence': 0.18,
                'frequency_based': 0.12
            },
            'feature_predictability': {
                'red_odd_even': 0.62,
                'red_size': 0.57,
                'blue_size': 0.50
            }
        }
    ]
    
    print("\n2️⃣ 测试优化策略")
    best_score = current_result['score']
    best_strategy = None
    best_params = current_params
    
    for i, strategy in enumerate(optimization_strategies, 1):
        print(f"\n测试策略 {i}: {strategy['name']}")
        
        result = evaluate_parameter_set(strategy)
        score = result['score']
        
        print(f"  得分: {score:.4f} (vs 当前 {current_result['score']:.4f})")
        print(f"  红球奇偶: {result['details']['red_odd_even_rate']:.3f}")
        print(f"  红球大小: {result['details']['red_size_rate']:.3f}")
        print(f"  蓝球大小: {result['details']['blue_size_rate']:.3f}")
        print(f"  2+1命中: {result['details']['total_2_1_rate']:.3f}")
        
        if score > best_score:
            best_score = score
            best_strategy = strategy
            best_params = strategy
            print(f"  🎯 发现更好的策略!")
    
    print("\n3️⃣ 优化结果总结")
    print("=" * 60)
    
    if best_strategy:
        improvement = best_score - current_result['score']
        print(f"✅ 找到更优参数组合!")
        print(f"最佳策略: {best_strategy['name']}")
        print(f"性能提升: {improvement:.4f} ({improvement/current_result['score']*100:.1f}%)")
        
        print(f"\n📊 最佳参数:")
        print(f"策略权重:")
        for key, value in best_params['strategy_weights'].items():
            print(f"  {key}: {value:.3f}")
        
        print(f"特征可预测性:")
        for key, value in best_params['feature_predictability'].items():
            print(f"  {key}: {value:.3f}")
        
        # 保存结果
        result_data = {
            'method': 'demo_quick_optimization',
            'best_score': best_score,
            'best_params': best_params,
            'best_strategy_name': best_strategy['name'],
            'current_score': current_result['score'],
            'improvement': improvement,
            'timestamp': datetime.now().isoformat()
        }
        
        filename = f"demo_optimization_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        print(f"可使用 apply_optimized_parameters.py 应用这些参数")
        
    else:
        print(f"❌ 未找到更好的参数组合")
        print(f"当前参数已经是测试范围内的最优配置")


if __name__ == "__main__":
    demo_quick_optimization()
